# This workflow will build a Java project with <PERSON><PERSON>
# For more information see: https://help.github.com/actions/language-and-framework-guides/building-and-testing-java-with-maven

name: Java CI with <PERSON><PERSON>

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 17
        uses: actions/setup-java@v2
        with:
          java-version: '17'
          distribution: 'adopt'
      - name: Build with <PERSON><PERSON>
        run: mvn -B package --file pom.xml
        env:
          GITHUB_ACTIONS: true
          WHATSAPP_STORE: ${{ secrets.WHATSAPP_STORE }}
          WHATSAPP_KEYS: ${{ secrets.WHATSAPP_KEYS }}
          WHATSAPP_CONTACT: ${{ secrets.WHATSAPP_CONTACT }}
          GPG_PASSWORD: ${{ secrets.GPG_PASSWORD }}
