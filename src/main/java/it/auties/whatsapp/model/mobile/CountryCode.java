package it.auties.whatsapp.model.mobile;

import java.util.Arrays;
import java.util.Optional;

public enum CountryCode {
    AFGHANISTAN("93", 412),
    ALBANIA("355", 276),
    ALGERIA("213", 603),
    AMERICAN_SAMOA("1-684", 544),
    ANDORRA("376", 213),
    ANGOLA("244", 631),
    ANGUILLA("1-264", 365),
    ANTIGUA_AND_BARBUDA("1-268", 344),
    ARGENTINA("54", 722),
    ARMENIA("374", 283),
    ARUBA("297", 363),
    AUSTRALIA("61", 505),
    AUSTRIA("43", 232),
    AZERBAIJAN("994", 400),
    BAHAMAS("1-242", 364),
    BAHRAIN("973", 426),
    BANGLADESH("880", 470),
    BARBADOS("1-246", 342),
    BELARUS("375", 257),
    BELGIUM("32", 206),
    BELIZE("501", 702),
    BENIN("229", 616),
    BERMUDA("1-441", 350),
    BHUTAN("975", 402),
    BOLIVIA("591", 736),
    BOSNIA_AND_HERZEGOVINA("387", 218),
    BOTSWANA("267", 652),
    BRAZIL("55", 724),
    BRITISH_VIRGIN_ISLANDS("1-284", 348),
    BRUNEI("673", 528),
    BULGARIA("359", 284),
    BURKINA_FASO("226", 613),
    BURUNDI("257", 642),
    CAMBODIA("855", 456),
    CAMEROON("237", 624),
    CAPE_VERDE("238", 625),
    CAYMAN_ISLANDS("1-345", 346),
    CENTRAL_AFRICAN_REPUBLIC("236", 623),
    CHAD("235", 622),
    CHILE("56", 730),
    CHINA("86", 454),
    COLOMBIA("57", 732),
    COMOROS("269", 654),
    COOK_ISLANDS("682", 548),
    COSTA_RICA("506", 712),
    CROATIA("385", 219),
    CUBA("53", 368),
    CYPRUS("357", 280),
    CZECH_REPUBLIC("420", 230),
    DEMOCRATIC_REPUBLIC_OF_THE_CONGO("243", 630),
    DENMARK("45", 238),
    DJIBOUTI("253", 638),
    DOMINICA("1-767", 366),
    ECUADOR("593", 740),
    EGYPT("20", 602),
    EL_SALVADOR("503", 706),
    EQUATORIAL_GUINEA("240", 627),
    ERITREA("291", 657),
    ESTONIA("372", 248),
    ETHIOPIA("251", 636),
    FALKLAND_ISLANDS("500", 750),
    FAROE_ISLANDS("298", 288),
    FIJI("679", 542),
    FINLAND("358", 244),
    FRANCE("33", 208),
    FRENCH_POLYNESIA("689", 547),
    GABON("241", 628),
    GAMBIA("220", 607),
    GEORGIA("995", 282),
    GERMANY("49", 262),
    GHANA("233", 620),
    GIBRALTAR("350", 266),
    GREECE("30", 202),
    GREENLAND("299", 290),
    GRENADA("1-473", 352),
    GUAM("1-671", 535),
    GUATEMALA("502", 704),
    GUINEA("224", 537),
    GUYANA("592", 738),
    HAITI("509", 372),
    HONDURAS("504", 708),
    HONG_KONG("852", 454),
    HUNGARY("36", 216),
    ICELAND("354", 274),
    INDIA("91", 404),
    INDONESIA("62", 510),
    IRAN("98", 432),
    IRAQ("964", 418),
    IRELAND("353", 234),
    ISRAEL("972", 425),
    ITALY("39", 222),
    IVORY_COAST("225", 612),
    JAMAICA("1-876", 338),
    JAPAN("81", 440),
    JORDAN("962", 416),
    KAZAKHSTAN("7", 401),
    KENYA("254", 639),
    KIRIBATI("686", 545),
    KOSOVO("383", 221),
    KUWAIT("965", 419),
    LATVIA("371", 247),
    LEBANON("961", 415),
    LESOTHO("266", 651),
    LIBERIA("231", 618),
    LIBYA("218", 606),
    LIECHTENSTEIN("423", 295),
    LITHUANIA("370", 246),
    LUXEMBOURG("352", 270),
    MACAO("853", 455),
    MACEDONIA("389", 294),
    MADAGASCAR("261", 646),
    MALAWI("265", 650),
    MALAYSIA("60", 502),
    MALDIVES("960", 472),
    MALI("223", 610),
    MALTA("356", 278),
    MARSHALL_ISLANDS("692", 551),
    MAURITANIA("222", 609),
    MAURITIUS("230", 617),
    MEXICO("52", 334),
    MICRONESIA("691", 550),
    MOLDOVA("373", 259),
    MONACO("377", 212),
    MONGOLIA("976", 428),
    MONTENEGRO("382", 297),
    MONTSERRAT("1-664", 354),
    MOROCCO("212", 604),
    MOZAMBIQUE("258", 643),
    MYANMAR("95", 414),
    NAMIBIA("264", 649),
    NAURU("674", 536),
    NEPAL("977", 429),
    NETHERLANDS("31", 204),
    NEW_CALEDONIA("687", 546),
    NEW_ZEALAND("64", 530),
    NICARAGUA("505", 710),
    NIGER("227", 614),
    NIGERIA("234", 621),
    NIUE("683", 555),
    NORTHERN_MARIANA_ISLANDS("1-670", 534),
    NORWAY("47", 242),
    OMAN("968", 226),
    PAKISTAN("92", 410),
    PALAU("680", 552),
    PALESTINE("970", 423),
    PANAMA("507", 714),
    PAPUA_NEW_GUINEA("675", 537),
    PARAGUAY("595", 744),
    PERU("51", 716),
    PHILIPPINES("63", 515),
    POLAND("48", 260),
    PORTUGAL("351", 268),
    QATAR("974", 427),
    REPUBLIC_OF_THE_CONGO("242", 630),
    ROMANIA("40", 226),
    RUSSIA("7", 250),
    RWANDA("250", 635),
    SAINT_HELENA("290", 658),
    SAINT_KITTS_AND_NEVIS("1-869", 356),
    SAINT_LUCIA("1-758", 358),
    SAINT_PIERRE_AND_MIQUELON("508", 308),
    SAINT_VINCENT_AND_THE_GRENADINES("1-784", 360),
    SAMOA("685", 544),
    SAN_MARINO("378", 292),
    SAO_TOME_AND_PRINCIPE("239", 626),
    SAUDI_ARABIA("966", 420),
    SENEGAL("221", 608),
    SERBIA("381", 220),
    SEYCHELLES("248", 633),
    SIERRA_LEONE("232", 619),
    SINGAPORE("65", 525),
    SLOVENIA("386", 293),
    SOLOMON_ISLANDS("677", 540),
    SOUTH_AFRICA("27", 655),
    SOUTH_SUDAN("211", 659),
    SPAIN("34", 214),
    SRI_LANKA("94", 413),
    SUDAN("249", 634),
    SURINAME("597", 746),
    SWAZILAND("268", 653),
    SWEDEN("46", 240),
    SWITZERLAND("41", 228),
    SYRIA("963", 417),
    TAIWAN("886", 466),
    TAJIKISTAN("992", 436),
    TANZANIA("255", 640),
    THAILAND("66", 520),
    TOGO("228", 615),
    TOKELAU("690", 554),
    TONGA("676", 539),
    TRINIDAD_AND_TOBAGO("1-868", 374),
    TUNISIA("216", 605),
    TURKEY("90", 286),
    TURKMENISTAN("993", 438),
    TURKS_AND_CAICOS_ISLANDS("1-649", 376),
    TUVALU("688", 553),
    US_VIRGIN_ISLANDS("1-340", 332),
    UGANDA("256", 641),
    UKRAINE("380", 255),
    UNITED_ARAB_EMIRATES("971", 424),
    UNITED_KINGDOM("44", 234),
    UNITED_STATES("1", 310),
    URUGUAY("598", 748),
    UZBEKISTAN("998", 434),
    VANUATU("678", 541),
    VATICAN("379", 225),
    VENEZUELA("58", 734),
    WALLIS_AND_FUTUNA("681", 543),
    YEMEN("967", 421),
    ZAMBIA("260", 645),
    ZIMBABWE("263", 648),
    EAST_TIMOR("670", 514),
    GUINEA_BISSAU("245", 632),
    LAOS("856", 457),
    NETHERLANDS_ANTILLES("599", 362),
    NORTH_KOREA("850", 467),
    REUNION("262", 647),
    SOUTH_KOREA("82", 450),
    VIETNAM("84", 452);

    private final String prefix;
    private final int mcc;

    CountryCode(String prefix, int mcc) {
        this.prefix = prefix;
        this.mcc = mcc;
    }

    public static Optional<CountryCode> ofPrefix(String prefix) {
        return prefix == null ? Optional.empty() : Arrays.stream(values())
                .filter(entry -> entry.prefix().equalsIgnoreCase(prefix))
                .findFirst();
    }

    public String prefix() {
        return prefix;
    }

    public int mcc() {
        return mcc;
    }

    public String mnc() {
        return "001";
    }
}