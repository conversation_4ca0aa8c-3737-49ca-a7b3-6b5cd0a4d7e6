package it.auties.whatsapp.model.sync;

import it.auties.protobuf.annotation.ProtobufMessageName;
import it.auties.protobuf.annotation.ProtobufProperty;
import it.auties.protobuf.model.ProtobufMessage;

import static it.auties.protobuf.model.ProtobufType.BOOL;

@ProtobufMessageName("Message.InitialSecurityNotificationSettingSync")
public record InitialSecurityNotificationSettingSync(
        @ProtobufProperty(index = 1, type = BOOL) boolean securityNotificationEnabled) implements ProtobufMessage {
}
