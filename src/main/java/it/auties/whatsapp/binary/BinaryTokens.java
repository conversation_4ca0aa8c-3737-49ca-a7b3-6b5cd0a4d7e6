package it.auties.whatsapp.binary;

import it.auties.whatsapp.model.companion.CompanionProperty;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public final class BinaryTokens {
    public static final List<String> SINGLE_BYTE = List.of("xmlstreamstart", "xmlstreamend", "s.whatsapp.net", "type", "participant", "from", "receipt", "id", "notification", "disappearing_mode", "status", "jid", "broadcast", "user", "devices", "device_hash", "to", "offline", "message", "result", "class", "xmlns", "duration", "notify", "iq", "t", "ack", "g.us", "enc", "urn:xmpp:whatsapp:push", "presence", "config_value", "picture", "verified_name", "config_code", "key-index-list", "contact", "mediatype", "routing_info", "edge_routing", "get", "read", "urn:xmpp:ping", "fallback_hostname", "0", "chatstate", "business_hours_config", "unavailable", "download_buckets", "skmsg", "verified_level", "composing", "handshake", "device-list", "media", "text", "fallback_ip4", "media_conn", "device", "creation", "location", "config", "item", "fallback_ip6", "count", "w:profile:picture", "image", "business", "2", "hostname", "call-creator", "display_name", "relaylatency", "platform", "abprops", "success", "msg", "offline_preview", "prop", "key-index", "v", "day_of_week", "pkmsg", "version", "1", "ping", "w:p", "download", "video", "set", "specific_hours", "props", "primary", "unknown", "hash", "commerce_experience", "last", "subscribe", "max_buckets", "call", "profile", "member_since_text", "close_time", "call-id", "sticker", "mode", "participants", "value", "query", "profile_options", "open_time", "code", "list", "host", "ts", "contacts", "upload", "lid", "preview", "update", "usync", "w:stats", "delivery", "auth_ttl", "context", "fail", "cart_enabled", "appdata", "category", "atn", "direct_connection", "decrypt-fail", "relay_id", "mmg-fallback.whatsapp.net", "target", "available", "name", "last_id", "mmg.whatsapp.net", "categories", "401", "is_new", "index", "tctoken", "ip4", "token_id", "latency", "recipient", "edit", "ip6", "add", "thumbnail-document", "26", "paused", "true", "identity", "stream:error", "key", "sidelist", "background", "audio", "3", "thumbnail-image", "biz-cover-photo", "cat", "gcm", "thumbnail-video", "error", "auth", "deny", "serial", "in", "registration", "thumbnail-link", "remove", "00", "gif", "thumbnail-gif", "tag", "capability", "multicast", "item-not-found", "description", "business_hours", "config_expo_key", "md-app-state", "expiration", "fallback", "ttl", "300", "md-msg-hist", "device_orientation", "out", "w:m", "open_24h", "side_list", "token", "inactive", "01", "document", "te2", "played", "encrypt", "msgr", "hide", "direct_path", "12", "state", "not-authorized", "url", "terminate", "signature", "status-revoke-delay", "02", "te", "linked_accounts", "trusted_contact", "timezone", "ptt", "kyc-id", "privacy_token", "readreceipts", "appointment_only", "address", "expected_ts", "privacy", "7", "android", "interactive", "device-identity", "enabled", "attribute_padding", "1080", "03", "screen_height");

    public static final List<String> DOUBLE_BYTE = List.of("read-self", "active", "fbns", "protocol", "reaction", "screen_width", "heartbeat", "deviceid", "2:47DEQpj8", "uploadfieldstat", "voip_settings", "retry", "priority", "longitude", "conflict", "false", "ig_professional", "replaced", "preaccept", "cover_photo", "uncompressed", "encopt", "ppic", "04", "passive", "status-revoke-drop", "keygen", "540", "offer", "rate", "opus", "latitude", "w:gp2", "ver", "4", "business_profile", "medium", "sender", "prev_v_id", "email", "website", "invited", "sign_credential", "05", "transport", "skey", "reason", "peer_abtest_bucket", "America/Sao_Paulo", "appid", "refresh", "100", "06", "404", "101", "104", "107", "102", "109", "103", "member_add_mode", "105", "transaction-id", "110", "106", "outgoing", "108", "111", "tokens", "followers", "ig_handle", "self_pid", "tue", "dec", "thu", "joinable", "peer_pid", "mon", "features", "wed", "peer_device_presence", "pn", "delete", "07", "fri", "audio_duration", "admin", "connected", "delta", "rcat", "disable", "collection", "08", "480", "sat", "phash", "all", "invite", "accept", "critical_unblock_low", "group_update", "signed_credential", "blinded_credential", "eph_setting", "net", "09", "background_location", "refresh_id", "Asia/Kolkata", "privacy_mode_ts", "account_sync", "voip_payload_type", "service_areas", "acs_public_key", "v_id", "0a", "fallback_class", "relay", "actual_actors", "metadata", "w:biz", "5", "connected-limit", "notice", "0b", "host_storage", "fb_page", "subject", "privatestats", "invis", "groupadd", "010", "note.m4r", "uuid", "0c", "8000", "sun", "372", "1020", "stage", "1200", "720", "canonical", "fb", "011", "video_duration", "0d", "1140", "superadmin", "012", "Opening.m4r", "keystore_attestation", "dleq_proof", "013", "timestamp", "ab_key", "w:sync:app:state", "0e", "vertical", "600", "p_v_id", "6", "likes", "014", "500", "1260", "creator", "0f", "rte", "destination", "group", "group_info", "syncd_anti_tampering_fatal_exception_enabled", "015", "dl_bw", "Asia/Jakarta", "vp8/h.264", "online", "1320", "fb:multiway", "10", "timeout", "016", "nse_retry", "urn:xmpp:whatsapp:dirty", "017", "a_v_id", "web_shops_chat_header_button_enabled", "nse_call", "inactive-upgrade", "none", "web", "groups", "2250", "mms_hot_content_timespan_in_seconds", "contact_blacklist", "nse_read", "suspended_group_deletion_notification", "binary_version", "018", "https://www.whatsapp.com/otp/copy/", "reg_push", "shops_hide_catalog_attachment_entrypoint", "server_sync", ".", "ephemeral_messages_allowed_values", "019", "mms_vcache_aggregation_enabled", "iphone", "America/Argentina/Buenos_Aires", "01a", "mms_vcard_autodownload_size_kb", "nse_ver", "shops_header_dropdown_menu_item", "dhash", "catalog_status", "communities_mvp_new_iqs_serverprop", "blocklist", "default", "11", "ephemeral_messages_enabled", "01b", "original_dimensions", "8", "mms4_media_retry_notification_encryption_enabled", "mms4_server_error_receipt_encryption_enabled", "original_image_url", "sync", "multiway", "420", "companion_enc_static", "shops_profile_drawer_entrypoint", "01c", "vcard_as_document_size_kb", "status_video_max_duration", "request_image_url", "01d", "regular_high", "s_t", "abt", "share_ext_min_preliminary_image_quality", "01e", "32", "syncd_key_rotation_enabled", "data_namespace", "md_downgrade_read_receipts2", "patch", "polltype", "ephemeral_messages_setting", "userrate", "15", "partial_pjpeg_bw_threshold", "played-self", "catalog_exists", "01f", "mute_v2", "reject", "dirty", "announcement", "020", "13", "9", "status_video_max_bitrate", "fb:thrift_iq", "offline_batch", "022", "full", "ctwa_first_business_reply_logging", "h.264", "smax_id", "group_description_length", "https://www.whatsapp.com/otp/code", "status_image_max_edge", "smb_upsell_business_profile_enabled", "021", "web_upgrade_to_md_modal", "14", "023", "s_o", "smaller_video_thumbs_status_enabled", "media_max_autodownload", "960", "blocking_status", "peer_msg", "joinable_group_call_client_version", "group_call_video_maximization_enabled", "return_snapshot", "high", "America/Mexico_City", "entry_point_block_logging_enabled", "pop", "024", "1050", "16", "1380", "one_tap_calling_in_group_chat_size", "regular_low", "inline_joinable_education_enabled", "hq_image_max_edge", "locked", "America/Bogota", "smb_biztools_deeplink_enabled", "status_image_quality", "1088", "025", "payments_upi_intent_transaction_limit", "voip", "w:g2", "027", "md_pin_chat_enabled", "026", "multi_scan_pjpeg_download_enabled", "shops_product_grid", "transaction_id", "ctwa_context_enabled", "20", "fna", "hq_image_quality", "alt_jpeg_doc_detection_quality", "group_call_max_participants", "pkey", "America/Belem", "image_max_kbytes", "web_cart_v1_1_order_message_changes_enabled", "ctwa_context_enterprise_enabled", "urn:xmpp:whatsapp:account", "840", "Asia/Kuala_Lumpur", "max_participants", "video_remux_after_repair_enabled", "stella_addressbook_restriction_type", "660", "900", "780", "context_menu_ios13_enabled", "mute-state", "ref", "payments_request_messages", "029", "frskmsg", "vcard_max_size_kb", "sample_buffer_gif_player_enabled", "match_last_seen", "510", "4983", "video_max_bitrate", "028", "w:comms:chat", "17", "frequently_forwarded_max", "groups_privacy_blacklist", "Asia/Karachi", "02a", "web_download_document_thumb_mms_enabled", "02b", "hist_sync", "biz_block_reasons_version", "1024", "18", "web_is_direct_connection_for_plm_transparent", "view_once_write", "file_max_size", "paid_convo_id", "online_privacy_setting", "video_max_edge", "view_once_read", "enhanced_storage_management", "multi_scan_pjpeg_encoding_enabled", "ctwa_context_forward_enabled", "video_transcode_downgrade_enable", "template_doc_mime_types", "hq_image_bw_threshold", "30", "body", "u_aud_limit_sil_restarts_ctrl", "other", "participating", "w:biz:directory", "1110", "vp8", "4018", "meta", "doc_detection_image_max_edge", "image_quality", "1170", "02c", "smb_upsell_chat_banner_enabled", "key_expiry_time_second", "pid", "stella_interop_enabled", "19", "linked_device_max_count", "md_device_sync_enabled", "02d", "02e", "360", "enhanced_block_enabled", "ephemeral_icon_in_forwarding", "paid_convo_status", "gif_provider", "project_name", "server-error", "canonical_url_validation_enabled", "wallpapers_v2", "syncd_clear_chat_delete_chat_enabled", "medianotify", "02f", "shops_required_tos_version", "vote", "reset_skey_on_id_change", "030", "image_max_edge", "multicast_limit_global", "ul_bw", "21", "25", "5000", "poll", "570", "22", "031", "1280", "WhatsApp", "032", "bloks_shops_enabled", "50", "upload_host_switching_enabled", "web_ctwa_context_compose_enabled", "ptt_forwarded_features_enabled", "unblocked", "partial_pjpeg_enabled", "fbid:devices", "height", "ephemeral_group_query_ts", "group_join_permissions", "order", "033", "alt_jpeg_status_quality", "migrate", "popular-bank", "win_uwp_deprecation_killswitch_enabled", "web_download_status_thumb_mms_enabled", "blocking", "url_text", "035", "web_forwarding_limit_to_groups", "1600", "val", "1000", "syncd_msg_date_enabled", "bank-ref-id", "max_subject", "payments_web_enabled", "web_upload_document_thumb_mms_enabled", "size", "request", "ephemeral", "24", "receipt_agg", "ptt_remember_play_position", "sampling_weight", "enc_rekey", "mute_always", "037", "034", "23", "036", "action", "click_to_chat_qr_enabled", "width", "disabled", "038", "md_blocklist_v2", "played_self_enabled", "web_buttons_message_enabled", "flow_id", "clear", "450", "fbid:thread", "bloks_session_state", "America/Lima", "attachment_picker_refresh", "download_host_switching_enabled", "1792", "u_aud_limit_sil_restarts_test2", "custom_urls", "device_fanout", "optimistic_upload", "2000", "key_cipher_suite", "web_smb_upsell_in_biz_profile_enabled", "e", "039", "siri_post_status_shortcut", "pair-device", "lg", "lc", "stream_attribution_url", "model", "mspjpeg_phash_gen", "catalog_send_all", "new_multi_vcards_ui", "share_biz_vcard_enabled", "-", "clean", "200", "md_blocklist_v2_server", "03b", "03a", "web_md_migration_experience", "ptt_conversation_waveform", "u_aud_limit_sil_restarts_test1", "64", "ptt_playback_speed_enabled", "web_product_list_message_enabled", "paid_convo_ts", "27", "manufacturer", "psp-routing", "grp_uii_cleanup", "ptt_draft_enabled", "03c", "business_initiated", "web_catalog_products_onoff", "web_upload_link_thumb_mms_enabled", "03e", "mediaretry", "35", "hfm_string_changes", "28", "America/Fortaleza", "max_keys", "md_mhfs_days", "streaming_upload_chunk_size", "5541", "040", "03d", "2675", "03f", "...", "512", "mute", "48", "041", "alt_jpeg_quality", "60", "042", "md_smb_quick_reply", "5183", "c", "1343", "40", "1230", "043", "044", "mms_cat_v1_forward_hot_override_enabled", "user_notice", "ptt_waveform_send", "047", "Asia/Calcutta", "250", "md_privacy_v2", "31", "29", "128", "md_messaging_enabled", "046", "crypto", "690", "045", "enc_iv", "75", "failure", "ptt_oot_playback", "AIzaSyDR5yfaG7OG8sMTUj8kfQEb8T9pN8BM6Lk", "w", "048", "2201", "web_large_files_ui", "Asia/Makassar", "812", "status_collapse_muted", "1334", "257", "2HP4dm", "049", "patches", "1290", "43cY6T", "America/Caracas", "web_sticker_maker", "campaign", "ptt_pausable_enabled", "33", "42", "attestation", "biz", "04b", "query_linked", "s", "125", "04a", "810", "availability", "1411", "responsiveness_v2_m1", "catalog_not_created", "34", "America/Santiago", "1465", "enc_p", "04d", "status_info", "04f", "key_version", "..", "04c", "04e", "md_group_notification", "1598", "1215", "web_cart_enabled", "37", "630", "1920", "2394", "-1", "vcard", "38", "elapsed", "36", "828", "peer", "pricing_category", "1245", "invalid", "stella_ios_enabled", "2687", "45", "1528", "39", "u_is_redial_audio_1104_ctrl", "1025", "1455", "58", "2524", "2603", "054", "bsp_system_message_enabled", "web_pip_redesign", "051", "verify_apps", "1974", "1272", "1322", "1755", "052", "70", "050", "1063", "1135", "1361", "80", "1096", "1828", "1851", "1251", "1921", "key_config_id", "1254", "1566", "1252", "2525", "critical_block", "1669", "max_available", "w:auth:backup:token", "product", "2530", "870", "1022", "participant_uuid", "web_cart_on_off", "1255", "1432", "1867", "41", "1415", "1440", "240", "1204", "1608", "1690", "1846", "1483", "1687", "1749", "69", "url_number", "053", "1325", "1040", "365", "59", "Asia/Riyadh", "1177", "test_recommended", "057", "1612", "43", "1061", "1518", "1635", "055", "1034", "1375", "750", "1430", "event_code", "1682", "503", "55", "865", "78", "1309", "1365", "44", "America/Guayaquil", "535", "LIMITED", "1377", "1613", "1420", "1599", "1822", "05a", "1681", "password", "1111", "1214", "1376", "1478", "47", "1082", "4282", "Europe/Istanbul", "1307", "46", "058", "1124", "256", "rate-overlimit", "retail", "u_a_socket_err_fix_succ_test", "1292", "1370", "1388", "520", "861", "psa", "regular", "1181", "1766", "05b", "1183", "1213", "1304", "1537", "1724", "profile_picture", "1071", "1314", "1605", "407", "990", "1710", "746", "pricing_model", "056", "059", "061", "1119", "6027", "65", "877", "1607", "05d", "917", "seen", "1516", "49", "470", "973", "1037", "1350", "1394", "1480", "1796", "keys", "794", "1536", "1594", "2378", "1333", "1524", "1825", "116", "309", "52", "808", "827", "909", "495", "1660", "361", "957", "google", "1357", "1565", "1967", "996", "1775", "586", "736", "1052", "1670", "bank", "177", "1416", "2194", "2222", "1454", "1839", "1275", "53", "997", "1629", "6028", "smba", "1378", "1410", "05c", "1849", "727", "create", "1559", "536", "1106", "1310", "1944", "670", "1297", "1316", "1762", "en", "1148", "1295", "1551", "1853", "1890", "1208", "1784", "7200", "05f", "178", "1283", "1332", "381", "643", "1056", "1238", "2024", "2387", "179", "981", "1547", "1705", "05e", "290", "903", "1069", "1285", "2436", "062", "251", "560", "582", "719", "56", "1700", "2321", "325", "448", "613", "777", "791", "51", "488", "902", "Asia/Almaty", "is_hidden", "1398", "1527", "1893", "1999", "2367", "2642", "237", "busy", "065", "067", "233", "590", "993", "1511", "54", "723", "860", "363", "487", "522", "605", "995", "1321", "1691", "1865", "2447", "2462", "NON_TRANSACTIONAL", "433", "871", "432", "1004", "1207", "2032", "2050", "2379", "2446", "279", "636", "703", "904", "248", "370", "691", "700", "1068", "1655", "2334", "060", "063", "364", "533", "534", "567", "1191", "1210", "1473", "1827", "069", "701", "2531", "514", "prev_dhash", "064", "496", "790", "1046", "1139", "1505", "1521", "1108", "207", "544", "637", "final", "1173", "1293", "1694", "1939", "1951", "1993", "2353", "2515", "504", "601", "857", "modify", "spam_request", "p_121_aa_1101_test4", "866", "1427", "1502", "1638", "1744", "2153", "068", "382", "725", "1704", "1864", "1990", "2003", "Asia/Dubai", "508", "531", "1387", "1474", "1632", "2307", "2386", "819", "2014", "066", "387", "1468", "1706", "2186", "2261", "471", "728", "1147", "1372", "1961");

    public static final int DICTIONARY_VERSION = 3;

    public static final List<Character> NUMBERS = List.of('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '-', '.', '�', '�', '�', '�');

    public static final List<Character> HEX = List.of('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F');

    public static final String NUMBERS_REGEX = "[^0-9.-]+?";

    public static final String HEX_REGEX = "[^0-9A-F]+?";

    public static final Map<Integer, CompanionProperty> PROPERTIES;

    static {
        var properties = new HashMap<Integer, CompanionProperty>();
        properties.put(1719, new CompanionProperty("order_details_total_order_minimum_value", 1719, 1, 1));
        properties.put(1684, new CompanionProperty("order_details_total_maximum_value", 1684, 5.0E8, 5.0E8));
        properties.put(1683, new CompanionProperty("order_details_total_minimum_value", 1683, 0, 0));
        properties.put(3240, new CompanionProperty("order_messages_ephemeral_exception_enabled", 3240, false, true));
        properties.put(233, new CompanionProperty("in_app_support_v2_enabled", 233, false, false));
        properties.put(379, new CompanionProperty("in_app_support_v2_locale_langs", 379, "", ""));
        properties.put(390, new CompanionProperty("in_app_support_v2_numbers", 390, "", ""));
        properties.put(1031, new CompanionProperty("in_app_support_v2_number_prefixes", 1031, "15517868", "15517868"));
        properties.put(4799, new CompanionProperty("in_app_support_capi_number_prefixes", 4799, "155178684", "155178684"));
        properties.put(819, new CompanionProperty("in_app_support_v2_jump_to_group", 819, false, false));
        properties.put(974, new CompanionProperty("in_app_support_v2_jump_to_group_wait_time_in_ms", 974, 5000.0, 5000.0));
        properties.put(2765, new CompanionProperty("quick_mute_enabled", 2765, false, false));
        properties.put(308, new CompanionProperty("groups_dogfooding_ui", 308, false, false));
        properties.put(309, new CompanionProperty("md_icdc_enabled", 309, false, false));
        properties.put(310, new CompanionProperty("md_icdc_hash_length", 310, 10, 10));
        properties.put(361, new CompanionProperty("played_self_enabled", 361, false, false));
        properties.put(407, new CompanionProperty("ephemeral_24h_duration", 407, false, true));
        properties.put(536, new CompanionProperty("disappearing_mode", 536, false, false));
        properties.put(605, new CompanionProperty("payments_expressive_backgrounds_enabled", 605, false, true));
        properties.put(432, new CompanionProperty("ephemeral_allow_group_members", 432, false, true));
        properties.put(470, new CompanionProperty("business_profile_refresh_m1_enabled", 470, false, true));
        properties.put(730, new CompanionProperty("num_days_key_index_list_expiration", 730, 35, 35));
        properties.put(731, new CompanionProperty("num_days_before_device_expiry_check", 731, 7, 7));
        properties.put(1098, new CompanionProperty("media_reupload_limit_mb", 1098, 100, 100));
        properties.put(1961, new CompanionProperty("portrait_thumb_enabled_chat", 1961, false, true));
        properties.put(1962, new CompanionProperty("portrait_thumb_enabled_status", 1962, false, true));
        properties.put(4787, new CompanionProperty("channels_video_limit_mb", 4787, 16, 16));
        properties.put(3185, new CompanionProperty("default_video_limit_mb", 3185, 16, 64));
        properties.put(4155, new CompanionProperty("default_video_limit_mb_newsletter", 4155, 16, 16));
        properties.put(3656, new CompanionProperty("default_gif_limit_mb", 3656, 16, 64));
        properties.put(3657, new CompanionProperty("default_audio_limit_mb", 3657, 16, 64));
        properties.put(3660, new CompanionProperty("default_media_limit_mb", 3660, 16, 64));
        properties.put(3934, new CompanionProperty("hd_video_label_enabled", 3934, false, true));
        properties.put(3935, new CompanionProperty("per_send_hd_video_setting_enabled", 3935, false, true));
        properties.put(4138, new CompanionProperty("per_send_hd_video_setting_for_groups_enabled", 4138, false, true));
        properties.put(3936, new CompanionProperty("hd_video_min_streaming_bandwidth", 3936, 150, 150));
        properties.put(4152, new CompanionProperty("hd_video_show_data_warning_dialog", 4152, false, true));
        properties.put(4153, new CompanionProperty("hd_video_data_warning_max_mb", 4153, 64, 64));
        properties.put(4171, new CompanionProperty("hd_video_definition_min_edge", 4171, 720, 720));
        properties.put(4172, new CompanionProperty("hd_video_definition_max_edge", 4172, 864, 864));
        properties.put(4175, new CompanionProperty("hd_video_definition_min_edge_with_max_edge", 4175, 480, 480));
        properties.put(535, new CompanionProperty("message_level_reporting", 535, false, true));
        properties.put(636, new CompanionProperty("native_shop_preview_enabled", 636, false, true));
        properties.put(736, new CompanionProperty("sync_archive_v2_setting", 736, false, false));
        properties.put(637, new CompanionProperty("ptt_conversation_waveform", 637, false, true));
        properties.put(746, new CompanionProperty("ptt_waveform_send", 746, false, true));
        properties.put(753, new CompanionProperty("adv_v2_m4_m5", 753, false, false));
        properties.put(903, new CompanionProperty("adv_v2_m6", 903, false, false));
        properties.put(777, new CompanionProperty("ptt_draft_enabled", 777, false, true));
        properties.put(871, new CompanionProperty("ptt_pausable_enabled", 871, false, true));
        properties.put(791, new CompanionProperty("tos_3_client_gating_enabled", 791, false, false));
        properties.put(877, new CompanionProperty("tos_client_state_fetch_enabled", 877, false, false));
        properties.put(908, new CompanionProperty("tos_client_state_fetch_iteration", 908, 0, 0));
        properties.put(1105, new CompanionProperty("country_client_gating_enabled", 1105, false, false));
        properties.put(1035, new CompanionProperty("system_msg_numbers_fb_branded", 1035, "16505434800,16503130062,16507885324,16508620604,16504228206,447710173736,16315551023,16505361212,16508129150,16315555102,16315558723,16505212669,16507885280,19032707825,0", "16505434800,16503130062,16507885324,16508620604,16504228206,447710173736,16315551023,16505361212,16508129150,16315555102,16315558723,16505212669,16507885280,19032707825,0"));
        properties.put(1036, new CompanionProperty("system_msg_numbers_fb_inc", 1036, "", ""));
        properties.put(1190, new CompanionProperty("log_clock_skew", 1190, false, false));
        properties.put(794, new CompanionProperty("trusted_contacts", 794, false, false));
        properties.put(995, new CompanionProperty("trusted_contacts_sender", 995, false, false));
        properties.put(922, new CompanionProperty("trusted_contacts_ti", 922, false, false));
        properties.put(865, new CompanionProperty("tctoken_duration", 865, 604800, 604800));
        properties.put(909, new CompanionProperty("tctoken_num_buckets", 909, 4, 4));
        properties.put(996, new CompanionProperty("tctoken_duration_sender", 996, 604800, 604800));
        properties.put(997, new CompanionProperty("tctoken_num_buckets_sender", 997, 4, 4));
        properties.put(827, new CompanionProperty("reactions_receive", 827, false, true));
        properties.put(828, new CompanionProperty("reactions_send", 828, false, true));
        properties.put(1150, new CompanionProperty("reactions_announcement_only", 1150, false, false));
        properties.put(987, new CompanionProperty("reaction_cleanup_days", 987, 31, 31));
        properties.put(1605, new CompanionProperty("reactions_chat_preview", 1605, false, true));
        properties.put(1361, new CompanionProperty("reactions_animations", 1361, false, true));
        properties.put(1485, new CompanionProperty("reactions_animations_simple", 1485, false, true));
        properties.put(861, new CompanionProperty("md_migration_experience", 861, 2, 2));
        properties.put(869, new CompanionProperty("web_abprop_direct_connection_md", 869, false, true));
        properties.put(907, new CompanionProperty("media_upload_prekeys_fetch_enabled", 907, false, true));
        properties.put(1828, new CompanionProperty("reactions_panel_prekeys_fetch_enabled", 1828, false, true));
        properties.put(1455, new CompanionProperty("status_quick_reply_enabled", 1455, false, true));
        properties.put(1974, new CompanionProperty("status_quick_reply_receiver_changes_enabled", 1974, false, true));
        properties.put(952, new CompanionProperty("ptt_remember_play_position", 952, false, true));
        properties.put(957, new CompanionProperty("banned_shops_ux_enabled", 957, false, true));
        properties.put(973, new CompanionProperty("group_suspend_v0_enabled", 973, false, true));
        properties.put(3181, new CompanionProperty("expiring_groups_enabled", 3181, false, false));
        properties.put(3864, new CompanionProperty("community_breakout_groups_enabled", 3864, false, true));
        properties.put(3795, new CompanionProperty("parent_group_directory_enabled", 3795, false, true));
        properties.put(5109, new CompanionProperty("parent_group_join_request_system_enabled", 5109, false, false));
        properties.put(4654, new CompanionProperty("parent_group_member_can_add_enabled", 4654, false, false));
        properties.put(5385, new CompanionProperty("parent_group_member_can_add_default_everyone_enabled", 5385, false, false));
        properties.put(982, new CompanionProperty("parent_group_view_enabled", 982, false, true));
        properties.put(1173, new CompanionProperty("parent_group_create_enabled", 1173, false, true));
        properties.put(1228, new CompanionProperty("parent_group_query_ts", 1228, 0, 0));
        properties.put(1238, new CompanionProperty("parent_group_link_limit", 1238, 100, 100));
        properties.put(3054, new CompanionProperty("allow_subgroup_admin_to_unlink", 3054, false, true));
        properties.put(3246, new CompanionProperty("community_creation_no_add_groups_screen", 3246, false, true));
        properties.put(2774, new CompanionProperty("community_announcement_group_size_limit", 2774, 5000.0, 5000.0));
        properties.put(5656, new CompanionProperty("parent_group_announcement_comments_banner", 5656, false, true));
        properties.put(3121, new CompanionProperty("community_announcement_improvement_m1", 3121, false, true));
        properties.put(3239, new CompanionProperty("community_announcement_improvement_m2", 3239, false, true));
        properties.put(3380, new CompanionProperty("community_announcement_improvement_m3", 3380, false, true));
        properties.put(4053, new CompanionProperty("community_creation_nux_always", 4053, false, false));
        properties.put(4071, new CompanionProperty("community_creation_nux_count", 4071, 1, 1));
        properties.put(3738, new CompanionProperty("community_subgroup_switcher_entrypoint_enabled", 3738, false, true));
        properties.put(3078, new CompanionProperty("community_subgroup_icon_variant", 3078, 0, 2));
        properties.put(4160, new CompanionProperty("community_subgroup_identity_v2", 4160, false, true));
        properties.put(5046, new CompanionProperty("community_history_setting_receive", 5046, false, false));
        properties.put(5191, new CompanionProperty("community_history_setting_send", 5191, false, false));
        properties.put(5192, new CompanionProperty("community_history_receive", 5192, false, false));
        properties.put(5193, new CompanionProperty("community_history_send", 5193, false, false));
        properties.put(1990, new CompanionProperty("parent_group_link_limit_community_creation", 1990, 10, 20));
        properties.put(1655, new CompanionProperty("parent_group_admins_limit", 1655, 20, 20));
        properties.put(2205, new CompanionProperty("parent_group_view_enabled_for_smb_on_web", 2205, false, true));
        properties.put(2206, new CompanionProperty("parent_group_create_enabled_for_smb_on_web", 2206, false, true));
        properties.put(2356, new CompanionProperty("parent_group_create_privacy", 2356, false, true));
        properties.put(2382, new CompanionProperty("parent_group_min_participants_for_group_entry_point", 2382, 20, 1));
        properties.put(2436, new CompanionProperty("parent_group_tap_to_request_enabled", 2436, false, true));
        properties.put(2446, new CompanionProperty("parent_group_tap_to_add_enabled", 2446, false, true));
        properties.put(2447, new CompanionProperty("parent_group_no_disclaimer", 2447, false, true));
        properties.put(3147, new CompanionProperty("parent_group_subgroup_filter", 3147, false, false));
        properties.put(3023, new CompanionProperty("community_groups_navigation", 3023, false, true));
        properties.put(3748, new CompanionProperty("community_chat_list_tabs", 3748, false, false));
        properties.put(3167, new CompanionProperty("parent_group_no_subgroup_requirement", 3167, false, true));
        properties.put(1864, new CompanionProperty("community_admin_promotion_one_time_prompt", 1864, false, false));
        properties.put(2307, new CompanionProperty("document_preview_caption_changes_enabled", 2307, false, true));
        properties.put(1040, new CompanionProperty("forwarded_ptt_ui_enabled", 1040, false, true));
        properties.put(1054, new CompanionProperty("shops_storefront_url_format", 1054, "https://www.facebook.com/%s/shop/", "https://www.facebook.com/%s/shop/"));
        properties.put(1135, new CompanionProperty("message_count_logging_md_enabled", 1135, false, false));
        properties.put(2430, new CompanionProperty("url_send_receive_logging_enabled", 2430, false, true));
        properties.put(2431, new CompanionProperty("inline_video_playback_additional_logging_enabled", 2431, false, true));
        properties.put(1064, new CompanionProperty("dev_prop_string", 1064, "", ""));
        properties.put(1065, new CompanionProperty("dev_prop_boolean", 1065, false, false));
        properties.put(1066, new CompanionProperty("dev_prop_int", 1066, 0, 0));
        properties.put(1067, new CompanionProperty("dev_prop_float", 1067, 0, 0));
        properties.put(3077, new CompanionProperty("disable_status_to_non_sub", 3077, false, false));
        properties.put(1107, new CompanionProperty("order_details_from_cart_enabled", 1107, false, true));
        properties.put(1176, new CompanionProperty("order_details_custom_item_enabled", 1176, false, true));
        properties.put(1212, new CompanionProperty("order_details_from_catalog_enabled", 1212, false, true));
        properties.put(1187, new CompanionProperty("md_app_state_critical_data_processing_logging", 1187, false, true));
        properties.put(1221, new CompanionProperty("md_app_state_report_md_sync_mutation_stats", 1221, false, true));
        properties.put(1188, new CompanionProperty("order_management_enabled", 1188, false, false));
        properties.put(1204, new CompanionProperty("growth_lock_v0_enabled", 1204, false, true));
        properties.put(1287, new CompanionProperty("smart_filters_enabled_consumer", 1287, false, true));
        properties.put(3554, new CompanionProperty("inbox_management_filters_m2", 3554, false, false));
        properties.put(4991, new CompanionProperty("enable_spam_report_iq_with_privacy_token", 4991, false, true));
        properties.put(4992, new CompanionProperty("enable_privacy_token_with_timestamp", 4992, false, true));
        properties.put(1517, new CompanionProperty("md_offline_v2_m2_enabled", 1517, 10, 10));
        properties.put(1533, new CompanionProperty("profile_photo_rings_for_status_enabled", 1533, false, true));
        properties.put(1534, new CompanionProperty("dc_edit_postcode_by_default_enabled", 1534, false, false));
        properties.put(2614, new CompanionProperty("media_picker_select_limit", 2614, 30, 30));
        properties.put(2693, new CompanionProperty("media_picker_select_limit_new", 2693, 30, 30));
        properties.put(1608, new CompanionProperty("chatlist_filters_v1", 1608, false, false));
        properties.put(1653, new CompanionProperty("community_suspend_v0_enabled", 1653, false, true));
        properties.put(1777, new CompanionProperty("is_meta_employee_or_internal_tester", 1777, false, false));
        properties.put(1838, new CompanionProperty("disable_auto_download", 1838, false, false));
        properties.put(2154, new CompanionProperty("community_tab_m2", 2154, false, true));
        properties.put(2281, new CompanionProperty("gif_autoplay_enabled", 2281, false, false));
        properties.put(3682, new CompanionProperty("gif_min_play_loops", 3682, 1, 1));
        properties.put(3683, new CompanionProperty("gif_max_play_loops", 3683, 3, 3));
        properties.put(3684, new CompanionProperty("gif_max_play_duration", 3684, 5, 5));
        properties.put(1868, new CompanionProperty("web_send_only_active_receipts", 1868, false, true));
        properties.put(2461, new CompanionProperty("num_days_hosted_device_signed_identity_signature_expiration", 2461, 90, 90));
        properties.put(2521, new CompanionProperty("cag_member_key_rotation_optimization", 2521, false, false));
        properties.put(2540, new CompanionProperty("elevated_push_names_v2_enabled", 2540, false, false));
        properties.put(2763, new CompanionProperty("elevated_push_names_v2_m1_follow_up_enabled", 2763, false, false));
        properties.put(2904, new CompanionProperty("elevated_push_names_v2_m2_enabled", 2904, false, false));
        properties.put(2588, new CompanionProperty("smb_capi_coexistence_enabled", 2588, false, true));
        properties.put(2633, new CompanionProperty("smb_client_side_linkshim_enabled", 2633, true, true));
        properties.put(2508, new CompanionProperty("web_non_blocking_offline_resume_max_message_count", 2508, 1000.0, 1000.0));
        properties.put(1809, new CompanionProperty("web_unified_flow", 1809, 0, 0));
        properties.put(2634, new CompanionProperty("smb_client_side_linkshim_signed_regexp", 2634, "https:\\/\\/n\\.wl\\.co\\/[^/]*\\/[^/]*\\/(.*)$", "https:\\/\\/n\\.wl\\.co\\/[^/]*\\/[^/]*\\/(.*)$"));
        properties.put(2639, new CompanionProperty("placeholder_message_key_hash_logging", 2639, false, true));
        properties.put(2795, new CompanionProperty("use_appdata_stanza_on_receiver", 2795, false, false));
        properties.put(2796, new CompanionProperty("use_appdata_stanza_on_sender", 2796, false, false));
        properties.put(2814, new CompanionProperty("web_lazy_pull", 2814, false, false));
        properties.put(3806, new CompanionProperty("msgd_drop_device_notifications", 3806, false, false));
        properties.put(3061, new CompanionProperty("media_large_file_awareness_popup_enabled", 3061, false, true));
        properties.put(3115, new CompanionProperty("media_large_file_awareness_popup_file_size_in_MB", 3115, 2048, 2048));
        properties.put(3069, new CompanionProperty("send_cag_member_revokes_as_GDM", 3069, true, true));
        properties.put(3079, new CompanionProperty("parent_group_remove_orphaned_members", 3079, false, true));
        properties.put(3292, new CompanionProperty("community_rich_system_message_enabled", 3292, false, false));
        properties.put(3097, new CompanionProperty("group_mentions_in_cag", 3097, false, true));
        properties.put(4087, new CompanionProperty("group_mentions_in_subgroups", 4087, false, true));
        properties.put(3267, new CompanionProperty("parent_group_home_header_actions_enabled", 3267, false, false));
        properties.put(3191, new CompanionProperty("non_blocking_resume_from_open_tab_enabled", 3191, false, false));
        properties.put(3622, new CompanionProperty("non_blocking_resume_from_open_tab_signal_enabled", 3622, false, false));
        properties.put(3247, new CompanionProperty("smb_catalog_messages_download_thumbnail_on_receiver_enabled", 3247, false, false));
        properties.put(3280, new CompanionProperty("send_extended_nack_enabled", 3280, false, false));
        properties.put(3741, new CompanionProperty("send_message_drop_nack_enabled", 3741, false, false));
        properties.put(4213, new CompanionProperty("send_message_drop_old_couter_nack_enabled", 4213, false, false));
        properties.put(3154, new CompanionProperty("parent_group_enhanced_description_enabled", 3154, false, true));
        properties.put(3616, new CompanionProperty("parent_group_info_updates_enabled", 3616, false, false));
        properties.put(3488, new CompanionProperty("noyb_opt_out_flag", 3488, false, false));
        properties.put(3664, new CompanionProperty("service_improvement_opt_out_flag", 3664, false, false));
        properties.put(3058, new CompanionProperty("wa_ctwa_web_entrypoint_home_header_enabled", 3058, false, false));
        properties.put(3095, new CompanionProperty("wa_ctwa_web_entrypoint_home_header_dropdown_enabled", 3095, false, false));
        properties.put(3096, new CompanionProperty("wa_ctwa_web_entrypoint_home_banner_enabled", 3096, false, false));
        properties.put(3242, new CompanionProperty("wa_ctwa_web_entrypoint_home_icon_tooltip_enabled", 3242, false, false));
        properties.put(3293, new CompanionProperty("wa_ctwa_web_entrypoint_pageless_enabled", 3293, false, false));
        properties.put(3376, new CompanionProperty("wa_ctwa_web_entrypoint_manage_ads_home_header_dropdown_enabled", 3376, false, false));
        properties.put(3294, new CompanionProperty("wa_ctwa_web_fetch_linked_accounts_enabled", 3294, false, false));
        properties.put(3695, new CompanionProperty("report_to_admin_kill_switch", 3695, false, true));
        properties.put(3696, new CompanionProperty("report_to_admin_enabled", 3696, false, true));
        properties.put(3829, new CompanionProperty("parent_group_allow_member_added_groups_m1", 3829, false, true));
        properties.put(4530, new CompanionProperty("parent_group_allow_member_added_groups_default_on_creation", 4530, false, true));
        properties.put(4184, new CompanionProperty("parent_group_allow_member_added_groups_m2", 4184, false, true));
        properties.put(5077, new CompanionProperty("parent_group_allow_member_suggest_existing_m3_sender", 5077, false, true));
        properties.put(5078, new CompanionProperty("parent_group_allow_member_suggest_existing_m3_receiver", 5078, false, true));
        properties.put(5562, new CompanionProperty("events_create", 5562, false, false));
        properties.put(5563, new CompanionProperty("events_view", 5563, false, false));
        properties.put(3224, new CompanionProperty("abort_building_e2e_proto_on_error", 3224, false, true));
        properties.put(4055, new CompanionProperty("abort_decrypting_e2e_on_error", 4055, false, true));
        properties.put(3966, new CompanionProperty("community_shorter_group_creation_enabled", 3966, false, false));
        properties.put(5103, new CompanionProperty("community_stacked_squircle_enabled", 5103, false, false));
        properties.put(5665, new CompanionProperty("community_general_chat_notification_followup_enabled", 5665, false, false));
        properties.put(5169, new CompanionProperty("community_navigate_to_unread_subgroup_enabled", 5169, false, false));
        properties.put(4003, new CompanionProperty("community_navigation", 4003, false, false));
        properties.put(4852, new CompanionProperty("community_examples", 4852, false, true));
        properties.put(5453, new CompanionProperty("community_general_chat_create_enabled", 5453, false, true));
        properties.put(5021, new CompanionProperty("community_general_chat_UI_enabled", 5021, false, true));
        properties.put(5144, new CompanionProperty("community_general_chat_max_auto_add_users", 5144, 1024, 1024));
        properties.put(4010, new CompanionProperty("bonsai_enabled", 4010, false, false));
        properties.put(5362, new CompanionProperty("bonsai_entry_point_enabled", 5362, false, false));
        properties.put(5459, new CompanionProperty("bonsai_waitlist_enabled", 5459, false, false));
        properties.put(4165, new CompanionProperty("bonsai_receiver_enabled", 4165, false, false));
        properties.put(5246, new CompanionProperty("bonsai_inline_feedback_enabled", 5246, false, false));
        properties.put(4416, new CompanionProperty("bonsai_ptt_enabled", 4416, false, false));
        properties.put(4417, new CompanionProperty("bonsai_update_interval", 4417, 86400, 86400));
        properties.put(5413, new CompanionProperty("bonsai_waitlist_update_interval", 5413, 21600, 21600));
        properties.put(4532, new CompanionProperty("bonsai_avatar_enabled", 4532, false, false));
        properties.put(4736, new CompanionProperty("bonsai_ti_timeout_duration_ms", 4736, 10000.0, 10000.0));
        properties.put(4974, new CompanionProperty("bonsai_word_streaming_enabled", 4974, false, false));
        properties.put(5150, new CompanionProperty("bonsai_streaming_chunk_latency", 5150, 0, 0));
        properties.put(5268, new CompanionProperty("bonsai_streaming_line_count_for_pinning", 5268, 4, 4));
        properties.put(5283, new CompanionProperty("bonsai_carousel_enabled", 5283, false, true));
        properties.put(5637, new CompanionProperty("bonsai_english_only", 5637, false, false));
        properties.put(4206, new CompanionProperty("web_mediaretry_notification_nack_enabled", 4206, false, false));
        properties.put(4274, new CompanionProperty("bot_response_futureproof_message_enabled", 4274, false, true));
        properties.put(4836, new CompanionProperty("low_cache_hit_rate_media_types", 4836, "ptt,audio,document,ppic", "ptt,audio,document,ppic"));
        properties.put(2898, new CompanionProperty("wa_ctwa_web_thread_ad_attribution_enabled", 2898, false, false));
        properties.put(1495, new CompanionProperty("wa_ctwa_ads_action_banner_enabled", 1495, false, true));
        properties.put(4021, new CompanionProperty("wa_ctwa_ads_action_banner_enabled_web", 4021, false, true));
        properties.put(4022, new CompanionProperty("wa_ctwa_action_banner_logging_enabled_web", 4022, false, true));
        properties.put(1841, new CompanionProperty("ctwa_data_max_length", 1841, 768, 768));
        properties.put(1866, new CompanionProperty("wa_ctwa_action_banner_logging_enabled", 1866, false, true));
        properties.put(2487, new CompanionProperty("wa_ctwa_web_dc_logging_enabled", 2487, false, false));
        properties.put(2934, new CompanionProperty("ctwa_smb_data_sharing_consent", 2934, false, true));
        properties.put(5615, new CompanionProperty("ctwa_smb_data_sharing_settings_killswitch", 5615, false, false));
        properties.put(3331, new CompanionProperty("ctwa_smb_data_sharing_opt_in_cool_off_period", 3331, 259200, 259200));
        properties.put(2935, new CompanionProperty("ctwa_consumer_data_sharing_consent", 2935, false, true));
        properties.put(2936, new CompanionProperty("mark_as_action", 2936, false, true));
        properties.put(3017, new CompanionProperty("pairless_logging_attribution_window", 3017, 7, 7));
        properties.put(3169, new CompanionProperty("wa_biz_tool_logging_improvement", 3169, false, true));
        properties.put(3793, new CompanionProperty("ctwa_additional_label_event_logging_enabled", 3793, false, true));
        properties.put(4761, new CompanionProperty("ctwa_enhanced_label_logging", 4761, false, true));
        properties.put(5151, new CompanionProperty("ctwa_clear_tracking", 5151, false, false));
        properties.put(4542, new CompanionProperty("in_app_comms_manage_ads_web_banner_campaign_enabled", 4542, false, true));
        properties.put(4427, new CompanionProperty("business_tool_enhanced_logging", 4427, false, false));
        properties.put(4796, new CompanionProperty("ctwa_value_holdout_h2_23_enabled", 4796, false, false));
        properties.put(5009, new CompanionProperty("smb_labels_ctwa_data_sharing", 5009, false, true));
        properties.put(5324, new CompanionProperty("smb_message_labels_ctwa_data_sharing", 5324, false, true));
        properties.put(5463, new CompanionProperty("smb_label_improvements_m2", 5463, false, true));
        properties.put(5554, new CompanionProperty("ctwa_manage_ads_tab_web", 5554, false, true));
        properties.put(5671, new CompanionProperty("ctwa_quick_reply_labels", 5671, false, false));
        properties.put(5719, new CompanionProperty("smb_business_action_bar_enabled", 5719, false, false));
        properties.put(1912, new CompanionProperty("ig_reels_music_attribution", 1912, false, true));
        properties.put(2167, new CompanionProperty("video_stream_buffering_ui_enabled", 2167, false, true));
        properties.put(3068, new CompanionProperty("original_quality_image_min_edge", 3068, 2560, 2560));
        properties.put(3306, new CompanionProperty("original_quality_data_warning_max_mb", 3306, 16, 16));
        properties.put(3307, new CompanionProperty("original_quality_show_data_warning_dialog", 3307, false, true));
        properties.put(3613, new CompanionProperty("original_quality_minimum_elements_to_show_data_warning_dialog", 3613, 20, 20));
        properties.put(2915, new CompanionProperty("maximum_group_size_for_rcat", 2915, 100, 100));
        properties.put(2957, new CompanionProperty("web_youtube_rcat_consumption_enabled", 2957, false, true));
        properties.put(3044, new CompanionProperty("web_youtube_rcat_chat_generation_enabled", 3044, false, true));
        properties.put(5178, new CompanionProperty("force_transcode_videos", 5178, false, false));
        properties.put(5179, new CompanionProperty("force_transcode_photos", 5179, false, false));
        properties.put(3273, new CompanionProperty("autodownload_update_in_group_chat", 3273, true, true));
        properties.put(5517, new CompanionProperty("autodownload_update_in_one_one_chat", 5517, false, true));
        properties.put(3116, new CompanionProperty("enable_receiving_hd_photo_quality", 3116, false, true));
        properties.put(3322, new CompanionProperty("enable_days_since_receive_logging", 3322, false, true));
        properties.put(3490, new CompanionProperty("additional_pre_logging_enabled", 3490, false, true));
        properties.put(3820, new CompanionProperty("client_message_id_media_download_log_enabled", 3820, false, true));
        properties.put(3491, new CompanionProperty("media_sender_client_logging_enabled", 3491, false, true));
        properties.put(3349, new CompanionProperty("hqp_log_enabled", 3349, false, true));
        properties.put(3455, new CompanionProperty("web_fix_media_conn_block_rule_parsing", 3455, false, false));
        properties.put(3522, new CompanionProperty("youtube_inline_playback_killswitch", 3522, false, false));
        properties.put(3787, new CompanionProperty("media_engagement_logging_enabled", 3787, false, false));
        properties.put(3844, new CompanionProperty("show_bottom_sheet_gallery", 3844, false, true));
        properties.put(4538, new CompanionProperty("max_pixels_size_allowed_for_image", 4538, 921600, 921600));
        properties.put(4631, new CompanionProperty("fun_stickers_locale_langs", 4631, "en", "en"));
        properties.put(4643, new CompanionProperty("fun_stickers_phase2_enabled", 4643, false, false));
        properties.put(5582, new CompanionProperty("enable_media_view_reply", 5582, false, true));
        properties.put(1522, new CompanionProperty("status_inline_link_preview_enabled", 1522, false, true));
        properties.put(1851, new CompanionProperty("text_status_url_logging_enabled", 1851, false, true));
        properties.put(1852, new CompanionProperty("status_reaction_emojis", 1852, "[128525, 128514, 128558, 128546, 128591, 128079, 127881, 128175]", "[128525, 128514, 128558, 128546, 128591, 128079, 127881, 128175]"));
        properties.put(1859, new CompanionProperty("status_reply_received_logging_enabled", 1859, false, true));
        properties.put(2032, new CompanionProperty("status_caption_link_detection_enabled", 2032, false, true));
        properties.put(2086, new CompanionProperty("status_view_error_type_logging_enabled", 2086, true, true));
        properties.put(2039, new CompanionProperty("status_from_me_unseen_enabled", 2039, false, true));
        properties.put(451, new CompanionProperty("smb_collections_enabled", 451, false, true));
        properties.put(582, new CompanionProperty("consumer_collections_enabled", 582, false, true));
        properties.put(724, new CompanionProperty("smb_collections_appeal_flow_enabled", 724, false, false));
        properties.put(1074, new CompanionProperty("smb_multi_device_awareness", 1074, false, true));
        properties.put(875, new CompanionProperty("smb_quick_replies_v2_enabled", 875, false, false));
        properties.put(1003, new CompanionProperty("smb_ecommerce_compliance_india_m4", 1003, false, true));
        properties.put(1192, new CompanionProperty("smb_ecommerce_compliance_india_m4_5", 1192, false, true));
        properties.put(1015, new CompanionProperty("smart_filters_enabled", 1015, false, true));
        properties.put(1022, new CompanionProperty("btm_threads_logging_enabled", 1022, false, true));
        properties.put(1034, new CompanionProperty("native_commerce_threads_logging_enabled", 1034, false, true));
        properties.put(1168, new CompanionProperty("threads_logging_observe_list_enabled", 1168, false, true));
        properties.put(1203, new CompanionProperty("smb_hide_unsupported_currency_price", 1203, false, true));
        properties.put(1215, new CompanionProperty("hyperlinked_phone_numbers_enabled", 1215, false, false));
        properties.put(1229, new CompanionProperty("smb_catkit_query_version", 1229, 1, 1));
        properties.put(1263, new CompanionProperty("smb_phase_out_not_a_business", 1263, false, true));
        properties.put(1771, new CompanionProperty("smb_phase_out_not_a_business_V2", 1771, false, true));
        properties.put(1251, new CompanionProperty("smb_threads_logging_enabled", 1251, false, true));
        properties.put(1252, new CompanionProperty("smb_click_to_chat_logging_enabled", 1252, false, true));
        properties.put(1253, new CompanionProperty("smb_broadcast_logging_enabled", 1253, false, true));
        properties.put(1254, new CompanionProperty("smb_status_logging_enabled", 1254, false, true));
        properties.put(1255, new CompanionProperty("smb_biz_profile_logging_enabled", 1255, false, true));
        properties.put(1256, new CompanionProperty("smb_registration_flow_logging_enabled", 1256, false, true));
        properties.put(1272, new CompanionProperty("btm_qpl_enabled", 1272, false, true));
        properties.put(1913, new CompanionProperty("smb_temp_cover_photo_privacy_messaging", 1913, false, true));
        properties.put(1949, new CompanionProperty("show_shops_sunset_banner", 1949, false, true));
        properties.put(3961, new CompanionProperty("vname_logging_and_debugging", 3961, true, true));
        properties.put(3969, new CompanionProperty("verified_business_numbers", 3969, "{}", """
                {"paytm":[917531875318, 919004990049]}"""));
        properties.put(4006, new CompanionProperty("verified_business_numbers_for_business_name_update", 4006, "", "917531875318,919004990049"));
        properties.put(5001, new CompanionProperty("vname_cert_deprecation", 5001, false, true));
        properties.put(5383, new CompanionProperty("enable_coex_system_message", 5383, false, true));
        properties.put(212, new CompanionProperty("qpl_enabled", 212, false, true));
        properties.put(215, new CompanionProperty("qpl_upload_delay", 215, 1440, 1));
        properties.put(466, new CompanionProperty("qpl_sampling_as_string", 466, """
                json:{"sampling":[]}""", """
                json:{"sampling":[]}"""));
        properties.put(1223, new CompanionProperty("qpl_initial_upload_delay", 1223, 5, 1));
        properties.put(1570, new CompanionProperty("is_meta_employee", 1570, false, false));
        properties.put(383, new CompanionProperty("should_deregister_on_syncd_fatal", 383, true, true));
        properties.put(559, new CompanionProperty("group_catch_up", 559, false, false));
        properties.put(591, new CompanionProperty("web_abprop_ctwa_context_compose_enabled", 591, false, false));
        properties.put(592, new CompanionProperty("web_abprop_group_description_length", 592, 0, 0));
        properties.put(593, new CompanionProperty("web_abprop_ephemeral_messages_allowed_values", 593, "604800", "604800"));
        properties.put(584, new CompanionProperty("web_abprop_collections_display", 584, false, false));
        properties.put(2312, new CompanionProperty("multi_select_from_chat_list", 2312, false, true));
        properties.put(585, new CompanionProperty("web_abprop_collections_management", 585, false, false));
        properties.put(600, new CompanionProperty("web_abprop_drop_full_history_sync", 600, false, false));
        properties.put(710, new CompanionProperty("web_abprop_business_profile_incomplete_nux_banner", 710, false, false));
        properties.put(711, new CompanionProperty("web_abprop_product_catalog_nux_banner", 711, false, false));
        properties.put(712, new CompanionProperty("web_abprop_click_nux_banner_migration", 712, false, false));
        properties.put(717, new CompanionProperty("web_abprop_ecommerce_compliance_india", 717, false, false));
        properties.put(826, new CompanionProperty("web_abprop_edit_ecommerce_compliance_india", 826, false, false));
        properties.put(726, new CompanionProperty("drop_last_name", 726, false, false));
        properties.put(734, new CompanionProperty("web_abprop_catalog_icon_on_top_bar", 734, false, false));
        properties.put(741, new CompanionProperty("web_abprop_collections_nux_banner", 741, false, false));
        properties.put(760, new CompanionProperty("nfm_rendering_enabled", 760, false, false));
        properties.put(761, new CompanionProperty("web_abprop_nux_cart_interstitial", 761, false, false));
        properties.put(763, new CompanionProperty("web_abprop_business_profile_refresh_status_enabled", 763, false, false));
        properties.put(764, new CompanionProperty("web_abprop_business_profile_refresh_linked_account_enabled", 764, false, false));
        properties.put(765, new CompanionProperty("web_abprop_business_profile_refresh_edit_cover_photo_enabled", 765, false, false));
        properties.put(766, new CompanionProperty("web_abprop_business_profile_refresh_cover_photo_view_enabled", 766, false, false));
        properties.put(809, new CompanionProperty("elevated_important_msg", 809, false, false));
        properties.put(837, new CompanionProperty("web_privacy_settings", 837, false, false));
        properties.put(1226, new CompanionProperty("web_privacy_settings_v2", 1226, false, false));
        properties.put(873, new CompanionProperty("web_status_psa", 873, false, false));
        properties.put(1095, new CompanionProperty("web_status_psa_history_sync", 1095, false, false));
        properties.put(1195, new CompanionProperty("web_2fa", 1195, false, false));
        properties.put(887, new CompanionProperty("web_abprop_stateful_enumeration_enabled", 887, true, true));
        properties.put(894, new CompanionProperty("web_abprop_block_catalog_creation_ecommerce_compliance_india", 894, false, false));
        properties.put(930, new CompanionProperty("web_sticker_store", 930, true, true));
        properties.put(937, new CompanionProperty("web_proactive_prekeys_fetch_group_size_limit", 937, 0, 0));
        properties.put(962, new CompanionProperty("web_favorite_stickers", 962, false, false));
        properties.put(984, new CompanionProperty("web_orchestrator_enabled_version", 984, "bucket", "bucket"));
        properties.put(1033, new CompanionProperty("web_wam_v5_enabled", 1033, false, false));
        properties.put(1114, new CompanionProperty("web_ps_v3_enabled", 1114, false, false));
        properties.put(1053, new CompanionProperty("web_shop_storefront_message", 1053, false, false));
        properties.put(1078, new CompanionProperty("web_identity_store_cache", 1078, false, false));
        properties.put(1086, new CompanionProperty("web_abprop_large_files_encryption_optimization", 1086, false, false));
        properties.put(1099, new CompanionProperty("web_send_invisible_msg_to_new_groups", 1099, false, false));
        properties.put(1100, new CompanionProperty("web_send_invisible_msg_min_group_size", 1100, 128, 128));
        properties.put(1945, new CompanionProperty("web_send_invisible_msg_max_group_size", 1945, 1024, 1024));
        properties.put(1171, new CompanionProperty("web_init_chat_batch_size", 1171, 100, 100));
        properties.put(1172, new CompanionProperty("web_init_chat_max_unread_message_count", 1172, 0, 0));
        properties.put(1174, new CompanionProperty("web_abprop_skip_file_copy_on_attach", 1174, false, false));
        properties.put(1179, new CompanionProperty("reaction_history_sync", 1179, false, false));
        properties.put(1189, new CompanionProperty("web_abprop_screen_sharing_enabled", 1189, false, false));
        properties.put(1205, new CompanionProperty("web_graphql_for_catalog_m1", 1205, false, false));
        properties.put(1224, new CompanionProperty("web_adaptive_offline_resume_enabled", 1224, false, false));
        properties.put(1225, new CompanionProperty("web_wa_signal_enabled", 1225, false, false));
        properties.put(1232, new CompanionProperty("web_gdpr_request_account_info_enabled", 1232, false, false));
        properties.put(1247, new CompanionProperty("web_abprop_document_resume_upload", 1247, false, false));
        properties.put(1759, new CompanionProperty("more_reactions_option_desktop_beta_rollout", 1759, true, true));
        properties.put(1796, new CompanionProperty("reactions_keyboard_hides_three_flags_desktop_beta_rollout", 1796, false, false));
        properties.put(1329, new CompanionProperty("web_rotate_sender_key_if_sent", 1329, false, false));
        properties.put(1383, new CompanionProperty("web_lru_cache_purge_logic_refactor", 1383, false, false));
        properties.put(1367, new CompanionProperty("companion_min_versions", 1367, "json:[]", "json:[]"));
        properties.put(1368, new CompanionProperty("comparion_force_upgrade", 1368, false, false));
        properties.put(1351, new CompanionProperty("web_abprop_business_profile_refresh_linked_accounts_killswitch", 1351, false, false));
        properties.put(1355, new CompanionProperty("web_default_pull_mode_enabled", 1355, false, false));
        properties.put(1371, new CompanionProperty("web_abprop_chatd_login_cookie_enabled", 1371, false, false));
        properties.put(1373, new CompanionProperty("web_prekeys_fetch_first_batch_size", 1373, 0, 0));
        properties.put(1379, new CompanionProperty("md_app_state_gate_D34336913", 1379, false, false));
        properties.put(1385, new CompanionProperty("web_address_capture_message_enabled", 1385, false, false));
        properties.put(1400, new CompanionProperty("syncd_periodic_sync_days", 1400, 0, 0));
        properties.put(1401, new CompanionProperty("web_enable_hyperlinked_phone_numbers_ps_logging", 1401, false, false));
        properties.put(1451, new CompanionProperty("web_get_maybe_me_user_optimization_enabled", 1451, false, false));
        properties.put(1461, new CompanionProperty("web_should_fatal_on_missing_patch", 1461, true, true));
        properties.put(1479, new CompanionProperty("web_reactions_send_desktop_beta_rollout", 1479, true, true));
        properties.put(1481, new CompanionProperty("web_abprop_remove_uploaded_files", 1481, false, false));
        properties.put(1496, new CompanionProperty("web_abprop_remove_downloaded_files", 1496, false, false));
        properties.put(2879, new CompanionProperty("web_killswitch_s310872_mitigation", 2879, false, false));
        properties.put(1507, new CompanionProperty("web_new_rich_text_input", 1507, true, true));
        properties.put(1513, new CompanionProperty("web_syncd_max_mutations_to_process_during_resume", 1513, 1000.0, 1000.0));
        properties.put(1593, new CompanionProperty("reactions_skin_tone_aggregation", 1593, false, false));
        properties.put(1623, new CompanionProperty("message_quick_reply", 1623, false, false));
        properties.put(1659, new CompanionProperty("web_quantity_controls_enabled", 1659, false, false));
        properties.put(1633, new CompanionProperty("web_unified_message_processing_enabled", 1633, false, false));
        properties.put(1643, new CompanionProperty("web_push_notifications", 1643, false, true));
        properties.put(3868, new CompanionProperty("web_push_notifications_super_users", 3868, false, false));
        properties.put(1676, new CompanionProperty("web_notification_settings_v2", 1676, false, true));
        properties.put(1675, new CompanionProperty("web_abprop_device_agnostic_voip", 1675, false, false));
        properties.put(1680, new CompanionProperty("web_abprop_screen_lock_enabled", 1680, false, false));
        properties.put(4790, new CompanionProperty("web_abprop_screen_lock_show_learn_more_link", 4790, false, false));
        properties.put(1726, new CompanionProperty("web_command_palette", 1726, true, true));
        properties.put(1745, new CompanionProperty("web_group_profile_editor", 1745, true, true));
        properties.put(4901, new CompanionProperty("web_group_profile_picture_stickers_label_fix", 4901, false, true));
        properties.put(1751, new CompanionProperty("web_quick_reply_authoring", 1751, false, false));
        properties.put(1752, new CompanionProperty("web_accidental_delete_for_me", 1752, true, true));
        properties.put(1753, new CompanionProperty("web_abprop_core_wam_runtime", 1753, false, false));
        properties.put(1757, new CompanionProperty("web_profile_picture_db_cache_disabled", 1757, false, false));
        properties.put(1773, new CompanionProperty("web_offline_resume_qpl_enabled", 1773, false, false));
        properties.put(1802, new CompanionProperty("web_offline_resume_m3_enabled", 1802, false, false));
        properties.put(1808, new CompanionProperty("web_syncd_fatal_fields_from_L1104589PRV2", 1808, false, false));
        properties.put(1816, new CompanionProperty("web_media_editor_blur_tool", 1816, true, true));
        properties.put(1824, new CompanionProperty("web_abprop_mute_notifications_on_app_focus", 1824, false, true));
        properties.put(2533, new CompanionProperty("web_auto_mute_256_groups_confirmation", 2533, false, false));
        properties.put(1850, new CompanionProperty("web_multi_skin_toned_emoji_picker", 1850, false, false));
        properties.put(1894, new CompanionProperty("web_message_send_cache_warming_up", 1894, false, true));
        properties.put(2801, new CompanionProperty("web_message_send_precalculate_icdc", 2801, false, true));
        properties.put(1902, new CompanionProperty("web_ptt_streamer_upload", 1902, false, true));
        properties.put(1910, new CompanionProperty("web_prekey_fetch_cache_warming_up", 1910, false, true));
        properties.put(1911, new CompanionProperty("web_history_sync_ui", 1911, false, false));
        properties.put(1932, new CompanionProperty("web_abprop_emoji_experimental_api", 1932, false, false));
        properties.put(1959, new CompanionProperty("web_new_media_caption_input", 1959, true, true));
        properties.put(1964, new CompanionProperty("web_chatlist_toggle", 1964, false, true));
        properties.put(1985, new CompanionProperty("web_electron_deprecation_windows_sideload_stage1_awareness", 1985, false, false));
        properties.put(1986, new CompanionProperty("web_electron_deprecation_windows_sideload_stage2_compatible_expiry_kickoff", 1986, false, false));
        properties.put(1987, new CompanionProperty("web_electron_deprecation_windows_sideload_stage2_compatible_expiry_delay", 1987, 0, 0));
        properties.put(1988, new CompanionProperty("web_electron_deprecation_windows_sideload_stage2_incompatible_expiry_kickoff", 1988, false, false));
        properties.put(1989, new CompanionProperty("web_electron_deprecation_windows_sideload_stage2_incompatible_expiry_delay", 1989, 0, 0));
        properties.put(5101, new CompanionProperty("web_electron_deprecation_mac_appstore_stage_1_awareness", 5101, false, false));
        properties.put(5018, new CompanionProperty("web_electron_deprecation_mac_sideload_stage_1_awareness", 5018, false, false));
        properties.put(5294, new CompanionProperty("web_electron_deprecation_mac_sideload_stage_1_bbar_dismiss_duration_days", 5294, 7, 7));
        properties.put(5019, new CompanionProperty("web_electron_deprecation_mac_sideload_stage_2_expiry_kickoff", 5019, false, false));
        properties.put(5020, new CompanionProperty("web_electron_deprecation_mac_sideload_stage_2_expiry_delay", 5020, 0, 0));
        properties.put(5364, new CompanionProperty("web_electron_deprecation_mac_appstore_stage_2_expiry_kickoff", 5364, false, false));
        properties.put(5365, new CompanionProperty("web_electron_deprecation_mac_appstore_stage_2_expiry_delay", 5365, 0, 0));
        properties.put(2016, new CompanionProperty("web_message_list_a11y_redesign", 2016, true, true));
        properties.put(2018, new CompanionProperty("web_enable_profile_pic_thumb_db_caching", 2018, false, false));
        properties.put(4327, new CompanionProperty("web_enable_profile_pic_thumb_download_over_mms4", 4327, false, false));
        properties.put(2056, new CompanionProperty("web_enable_biz_catalog_view_ps_logging", 2056, true, true));
        properties.put(2063, new CompanionProperty("web_abprop_media_links_docs_search", 2063, false, false));
        properties.put(2179, new CompanionProperty("web_poll_creation_desktop_beta_rollout", 2179, false, false));
        properties.put(2181, new CompanionProperty("web_poll_receiving_desktop_beta_rollout", 2181, false, false));
        properties.put(2210, new CompanionProperty("web_file_streaming_upload", 2210, false, false));
        properties.put(2220, new CompanionProperty("web_new_group_member_search", 2220, false, false));
        properties.put(2264, new CompanionProperty("web_max_contacts_to_show_common_groups", 2264, 10, 10));
        properties.put(2268, new CompanionProperty("web_max_found_common_groups_displayed", 2268, 15, 15));
        properties.put(2231, new CompanionProperty("web_fp_reparsing_for_non_add_ons", 2231, false, false));
        properties.put(2280, new CompanionProperty("web_message_custom_aria_label", 2280, false, false));
        properties.put(2294, new CompanionProperty("web_message_list_a11y_redesign_beta_only", 2294, true, true));
        properties.put(4768, new CompanionProperty("web_search_by_type_date_infra", 4768, false, false));
        properties.put(4769, new CompanionProperty("web_search_by_type_enabled", 4769, false, false));
        properties.put(4770, new CompanionProperty("web_search_by_date_enabled", 4770, false, false));
        properties.put(2303, new CompanionProperty("web_poll_spam_report", 2303, false, false));
        properties.put(2322, new CompanionProperty("web_electron_active_reload", 2322, true, true));
        properties.put(2348, new CompanionProperty("desktop_upsell_win_butterbar", 2348, false, false));
        properties.put(2349, new CompanionProperty("desktop_upsell_win_ctas", 2349, false, false));
        properties.put(2725, new CompanionProperty("desktop_upsell_win_dropdown_btn", 2725, false, false));
        properties.put(4802, new CompanionProperty("desktop_upsell_win_temporary_ctas", 4802, false, false));
        properties.put(4803, new CompanionProperty("desktop_upsell_mac_temporary_ctas", 4803, false, false));
        properties.put(4804, new CompanionProperty("desktop_upsell_win_permanent_ctas", 4804, false, false));
        properties.put(4805, new CompanionProperty("desktop_upsell_mac_permanent_ctas", 4805, false, false));
        properties.put(5145, new CompanionProperty("desktop_upsell_win_cta_chatlist_dropdown", 5145, false, false));
        properties.put(5146, new CompanionProperty("desktop_upsell_win_cta_chatlist_toastbar", 5146, false, false));
        properties.put(5147, new CompanionProperty("desktop_upsell_win_cta_search_results_toastbar", 5147, false, false));
        properties.put(5148, new CompanionProperty("desktop_upsell_win_cta_call_btn", 5148, false, false));
        properties.put(5149, new CompanionProperty("desktop_upsell_win_cta_intro_panel", 5149, false, false));
        properties.put(2486, new CompanionProperty("documents_with_captions_send_desktop_beta_rollout", 2486, false, false));
        properties.put(2512, new CompanionProperty("profile_photo_rings_for_status_on_web_enabled", 2512, false, true));
        properties.put(2513, new CompanionProperty("voice_status_receipt_on_web_enabled", 2513, false, true));
        properties.put(2534, new CompanionProperty("web_crypto_library_enabled", 2534, false, false));
        properties.put(5320, new CompanionProperty("web_crypto_library_verification_enabled", 5320, false, false));
        properties.put(4583, new CompanionProperty("web_crypto_library_with_queues_enabled", 4583, false, false));
        properties.put(2543, new CompanionProperty("group_chat_profile_pictures_enabled_web_beta_rollout", 2543, true, true));
        properties.put(2545, new CompanionProperty("web_message_plugin_backend_registration_enabled", 2545, false, false));
        properties.put(2549, new CompanionProperty("query_verified_name_when_msg_differs", 2549, true, true));
        properties.put(2555, new CompanionProperty("web_media_auto_download_enabled", 2555, false, true));
        properties.put(2556, new CompanionProperty("web_media_auto_download_desktop_beta_enabled", 2556, false, true));
        properties.put(2566, new CompanionProperty("link_preview_wait_time", 2566, 7, 7));
        properties.put(2622, new CompanionProperty("web_screen_lock_max_retries", 2622, 10, 10));
        properties.put(2664, new CompanionProperty("forward_media_with_caption_desktop_beta_rollout", 2664, true, true));
        properties.put(2708, new CompanionProperty("web_new_status_reply_input", 2708, true, true));
        properties.put(2715, new CompanionProperty("web_display_name_for_enterprise_biz_vlevel_low_killswitch", 2715, false, false));
        properties.put(2716, new CompanionProperty("web_display_name_for_biz_vlevel_low_killswitch", 2716, true, true));
        properties.put(2793, new CompanionProperty("web_message_plugin_frontend_registration_enabled", 2793, false, false));
        properties.put(3081, new CompanionProperty("external_beta_can_join", 3081, false, true));
        properties.put(3031, new CompanionProperty("web_native_fetch_media_download", 3031, false, false));
        properties.put(3042, new CompanionProperty("web_image_max_edge", 3042, 1600, 1600));
        properties.put(3204, new CompanionProperty("web_image_max_hd_edge", 3204, 2560, 2560));
        properties.put(3118, new CompanionProperty("enable_logging_multi_select_from_chat_list", 3118, false, true));
        properties.put(3133, new CompanionProperty("web_store_quota_manager_enabled", 3133, false, false));
        properties.put(4670, new CompanionProperty("web_chat_with_unknown_contacts", 4670, false, false));
        properties.put(3134, new CompanionProperty("web_browser_quota_threshold", 3134, 100, 100));
        properties.put(3135, new CompanionProperty("web_browser_min_storage_quota", 3135, 5, 5));
        properties.put(3136, new CompanionProperty("web_original_photo_quality_upload_enabled", 3136, false, false));
        properties.put(3152, new CompanionProperty("web_deprecate_mms4_hash_based_download", 3152, false, true));
        properties.put(3164, new CompanionProperty("web_md5_message_key", 3164, false, true));
        properties.put(3729, new CompanionProperty("web_sha256_message_key", 3729, true, true));
        properties.put(3234, new CompanionProperty("web_e2e_backfill_expire_time", 3234, 5, 60));
        properties.put(3279, new CompanionProperty("web_message_table_index_rowid_optimization", 3279, false, false));
        properties.put(3350, new CompanionProperty("wds_radius_and_casing", 3350, false, true));
        properties.put(4032, new CompanionProperty("web_attach_menu_redesign", 4032, false, true));
        properties.put(3420, new CompanionProperty("web_expression_panels", 3420, false, false));
        properties.put(3600, new CompanionProperty("can_support_web_column_packing", 3600, false, false));
        properties.put(3970, new CompanionProperty("web_column_data_serialization_enabled", 3970, false, false));
        properties.put(3973, new CompanionProperty("column_serialization_perf_impact_test", 3973, false, false));
        properties.put(3723, new CompanionProperty("web_message_edit_receive_desktop_beta_rollout", 3723, false, false));
        properties.put(3724, new CompanionProperty("web_message_edit_send_desktop_beta_rollout", 3724, false, false));
        properties.put(3883, new CompanionProperty("web_message_edit_processing_reply_messages", 3883, true, true));
        properties.put(3728, new CompanionProperty("web_message_processing_cache_size", 3728, 400, 400));
        properties.put(3779, new CompanionProperty("web_encryption_failed_message_resend", 3779, false, false));
        properties.put(3818, new CompanionProperty("append_message_when_forwarding_media_desktop_beta", 3818, false, false));
        properties.put(3890, new CompanionProperty("web_client_pull_timeout_ms", 3890, 10000.0, 10000.0));
        properties.put(3892, new CompanionProperty("web_socket_reconnect_enabled", 3892, false, false));
        properties.put(4019, new CompanionProperty("web_outgoing_message_validation_list", 4019, "[]", "[]"));
        properties.put(4024, new CompanionProperty("web_device_sync_manager_enabled", 4024, false, false));
        properties.put(4453, new CompanionProperty("web_device_sync_manager_group_enabled", 4453, false, false));
        properties.put(4125, new CompanionProperty("web_draft_message_enabled", 4125, false, false));
        properties.put(4149, new CompanionProperty("history_sync_loop_interval_ms", 4149, 20000.0, 20000.0));
        properties.put(4364, new CompanionProperty("history_sync_on_demand_failure_limit", 4364, 10, 10));
        properties.put(4365, new CompanionProperty("history_sync_on_demand_cooldown_sec", 4365, 7200, 7200));
        properties.put(4366, new CompanionProperty("history_sync_on_demand_request_send_killswitch", 4366, true, true));
        properties.put(4390, new CompanionProperty("flattened_reactions_collection", 4390, false, false));
        properties.put(4403, new CompanionProperty("web_offline_notification_priority", 4403, false, false));
        properties.put(4404, new CompanionProperty("web_status_posting_enabled", 4404, false, false));
        properties.put(4475, new CompanionProperty("unified_pin_addon_infra_enabled", 4475, false, false));
        properties.put(4483, new CompanionProperty("web_enable_open_tab_pre_ack", 4483, false, false));
        properties.put(4669, new CompanionProperty("web_improved_text_tool_enabled", 4669, false, false));
        properties.put(4681, new CompanionProperty("web_internal_in_app_bug_reporting_enable", 4681, false, false));
        properties.put(4726, new CompanionProperty("web_sticker_suggestions_enable", 4726, false, false));
        properties.put(4724, new CompanionProperty("web_enable_capi_support_chat", 4724, false, false));
        properties.put(4792, new CompanionProperty("web_device_switching", 4792, false, true));
        properties.put(4904, new CompanionProperty("web_initial_sync_encrypted_msgs_storing", 4904, false, false));
        properties.put(4951, new CompanionProperty("web_expression_panels_mitigations", 4951, false, true));
        properties.put(4952, new CompanionProperty("web_animate_messages", 4952, false, false));
        properties.put(5523, new CompanionProperty("web_animate_new_messages", 5523, false, true));
        properties.put(4973, new CompanionProperty("web_improved_message_composer_enabled", 4973, false, false));
        properties.put(5079, new CompanionProperty("web_preload_chat_messages", 5079, false, true));
        properties.put(5080, new CompanionProperty("web_anyone_can_add_group_setting_enabled", 5080, false, true));
        properties.put(5100, new CompanionProperty("web_command_palette_plugins", 5100, false, false));
        properties.put(5106, new CompanionProperty("web_noncritical_history_sync_message_processing_break_iteration", 5106, 100, 100));
        properties.put(5110, new CompanionProperty("web_tc_token_db_read_enabled", 5110, false, false));
        properties.put(5164, new CompanionProperty("web_invalid_message_count_validation", 5164, false, true));
        properties.put(5165, new CompanionProperty("web_invalid_media_message_validation", 5165, false, true));
        properties.put(5271, new CompanionProperty("web_offline_dynamic_batch_size_enabled", 5271, false, true));
        properties.put(5297, new CompanionProperty("web_offline_dynamic_batch_config", 5297, """
                {"denominator": 2}""", """
                {"denominator": 2}"""));
        properties.put(5291, new CompanionProperty("web_history_sync_notification_handling_queue_v2", 5291, false, false));
        properties.put(5346, new CompanionProperty("web_evolve_about_receive_enabled", 5346, false, false));
        properties.put(5347, new CompanionProperty("web_evolve_about_send_enabled", 5347, false, false));
        properties.put(5388, new CompanionProperty("web_abort_building_e2e_proto_on_error", 5388, false, true));
        properties.put(5389, new CompanionProperty("web_abort_decrypting_e2e_on_error", 5389, false, true));
        properties.put(5410, new CompanionProperty("web_offline_progress_toastbar", 5410, false, true));
        properties.put(5447, new CompanionProperty("web_quoted_generate_msg_data", 5447, false, false));
        properties.put(5461, new CompanionProperty("web_resume_optimized_read_receipt_send_active_chat", 5461, false, true));
        properties.put(5502, new CompanionProperty("web_resume_optimized_read_receipt_send_interval", 5502, 500, 500));
        properties.put(5520, new CompanionProperty("web_pre_acks_m2_enabled", 5520, false, false));
        properties.put(5521, new CompanionProperty("web_pre_acks_m3_enabled", 5521, false, false));
        properties.put(5564, new CompanionProperty("web_push_notifications_receipt_handling_enabled", 5564, false, false));
        properties.put(5565, new CompanionProperty("desktop_upsell_mac_cta_chatlist_dropdown", 5565, false, false));
        properties.put(5566, new CompanionProperty("desktop_upsell_mac_cta_chatlist_toastbar", 5566, false, false));
        properties.put(5567, new CompanionProperty("desktop_upsell_mac_cta_search_results_toastbar", 5567, false, false));
        properties.put(5568, new CompanionProperty("desktop_upsell_mac_cta_call_btn", 5568, false, false));
        properties.put(5569, new CompanionProperty("desktop_upsell_mac_cta_intro_panel", 5569, false, false));
        properties.put(5677, new CompanionProperty("web_add_non_contacts_to_groups_enabled", 5677, false, false));
        properties.put(5680, new CompanionProperty("web_resume_optimized_message_post_processing_enabled", 5680, false, true));
        properties.put(5708, new CompanionProperty("web_biz_tools_on_navbar_enabled", 5708, false, false));
        properties.put(315, new CompanionProperty("stop_abprops_traffic_in_serverprops_response", 315, false, false));
        properties.put(3689, new CompanionProperty("chat_upsell_for_1on1_invites", 3689, false, false));
        properties.put(4118, new CompanionProperty("ugc_participant_limit", 4118, 5, 5));
        properties.put(4441, new CompanionProperty("anyone_can_add_to_groups_by_default", 4441, false, false));
        properties.put(4593, new CompanionProperty("group_join_request_on_by_default", 4593, false, false));
        properties.put(1825, new CompanionProperty("group_chat_profile_pictures_enabled", 1825, false, false));
        properties.put(3261, new CompanionProperty("group_chat_profile_pictures_v2_enabled", 3261, false, false));
        properties.put(3523, new CompanionProperty("unified_user_profile_navigation_enabled", 3523, false, false));
        properties.put(4215, new CompanionProperty("view_all_replies_enabled", 4215, false, false));
        properties.put(4545, new CompanionProperty("top_menu_redesign_enabled", 4545, false, false));
        properties.put(3010, new CompanionProperty("ugr_enabled", 3010, false, true));
        properties.put(3011, new CompanionProperty("ugc_enabled", 3011, false, true));
        properties.put(5002, new CompanionProperty("ug_chat_banner_enabled", 5002, false, true));
        properties.put(5016, new CompanionProperty("ug_chat_banner_visibility_max_seconds", 5016, 432000.0, 432000.0));
        properties.put(5119, new CompanionProperty("ug_chat_banner_visibility_min_seconds", 5119, 0, 0));
        properties.put(3088, new CompanionProperty("reword_subject_to_group_name_enabled", 3088, false, true));
        properties.put(1693, new CompanionProperty("commerce_metadata_supported_business", 1693, "***********,918591749310,917977079770,***********,5515997781156,5511989238421,555191894444,905333860133,908502213040,5511916282555,555139214004,555198849745,551147664020,622150851766,551121038525", "***********,447766028329,918591749310,917977079770,***********,5515997781156,5511989238421,555191894444,905333860133,908502213040,5511916282555,555139214004,555198849745,551147664020,622150851766"));
        properties.put(1607, new CompanionProperty("in_app_survey_phone_numbers", 1607, "***********", "***********"));
        properties.put(1595, new CompanionProperty("order_details_payment_instructions_enabled", 1595, false, true));
        properties.put(455, new CompanionProperty("enable_biz_activity_report_request", 455, false, false));
        properties.put(464, new CompanionProperty("plm_products_max_batch_fetch_size", 464, 18, 18));
        properties.put(550, new CompanionProperty("enable_granular_reject_reasons", 550, false, false));
        properties.put(604, new CompanionProperty("elevating_profile_names_enabled", 604, false, false));
        properties.put(689, new CompanionProperty("enable_group_profile_editor", 689, false, false));
        properties.put(690, new CompanionProperty("csat_message_rating", 690, false, true));
        properties.put(810, new CompanionProperty("facebook_link_preview_use_thumbnail", 810, false, true));
        properties.put(838, new CompanionProperty("tam_attachment_cache_compaction_enabled", 838, false, false));
        properties.put(853, new CompanionProperty("business_threads_logging_enabled", 853, false, false));
        properties.put(904, new CompanionProperty("private_stats_biz_view_logging_enabled", 904, false, false));
        properties.put(2367, new CompanionProperty("group_join_request_m0_anyone_can_join", 2367, false, false));
        properties.put(1727, new CompanionProperty("group_join_request_m1", 1727, false, false));
        properties.put(1728, new CompanionProperty("group_join_request_m2", 1728, false, false));
        properties.put(4727, new CompanionProperty("parent_group_announcement_comments_enabled", 4727, false, false));
        properties.put(5141, new CompanionProperty("parent_group_announcement_comments_receiver_enabled", 5141, false, false));
        properties.put(5660, new CompanionProperty("parent_group_announcement_comments_sender_use_lid", 5660, true, true));
        properties.put(4728, new CompanionProperty("parent_group_announcement_comments_participant_limit", 4728, 1024, 1024));
        properties.put(4729, new CompanionProperty("parent_group_announcement_comment_subscription_enabled", 4729, false, false));
        properties.put(1887, new CompanionProperty("group_join_request_m2_setting", 1887, false, false));
        properties.put(2913, new CompanionProperty("group_join_request_m2_logging", 2913, false, false));
        properties.put(2418, new CompanionProperty("group_join_request_m2_max_pending_participants_limit", 2418, 2, 2));
        properties.put(2369, new CompanionProperty("group_join_request_m3", 2369, false, false));
        properties.put(3451, new CompanionProperty("group_join_request_m3_sort_by_time", 3451, false, false));
        properties.put(3571, new CompanionProperty("group_join_request_m3_invited_tab", 3571, false, false));
        properties.put(3895, new CompanionProperty("group_join_request_m3_groups_in_common", 3895, false, false));
        properties.put(3452, new CompanionProperty("group_join_request_m3_banner", 3452, false, false));
        properties.put(5212, new CompanionProperty("group_join_request_m3_push_notification", 5212, false, false));
        properties.put(3382, new CompanionProperty("group_join_request_optional_message_soak", 3382, false, false));
        properties.put(3383, new CompanionProperty("group_join_request_can_view_optional_message", 3383, false, false));
        properties.put(3384, new CompanionProperty("group_join_request_can_send_optional_message", 3384, false, false));
        properties.put(2376, new CompanionProperty("group_join_request_m2_pushname", 2376, false, true));
        properties.put(2449, new CompanionProperty("group_join_request_m2_banner_on_conversation", 2449, false, false));
        properties.put(2749, new CompanionProperty("group_invite_new_bottom_sheet_enabled", 2749, true, true));
        properties.put(1967, new CompanionProperty("note_to_self", 1967, false, true));
        properties.put(2630, new CompanionProperty("note_to_self_entry_point", 2630, false, true));
        properties.put(1011, new CompanionProperty("no_delete_message_time_limit", 1011, false, false));
        properties.put(1333, new CompanionProperty("sender_revoke_window_sender", 1333, false, true));
        properties.put(1334, new CompanionProperty("sender_revoke_window_receiver", 1334, false, true));
        properties.put(1335, new CompanionProperty("sender_revoke_ui", 1335, false, true));
        properties.put(1177, new CompanionProperty("admin_revoke_receiver", 1177, false, true));
        properties.put(1292, new CompanionProperty("admin_revoke_sender", 1292, false, true));
        properties.put(1245, new CompanionProperty("admin_revoke_history_sync_consumer", 1245, false, true));
        properties.put(1865, new CompanionProperty("revokes_logging_unsampled", 1865, true, true));
        properties.put(3138, new CompanionProperty("pinned_messages_m0", 3138, false, false));
        properties.put(3139, new CompanionProperty("pinned_messages_m1_receiver", 3139, false, false));
        properties.put(5474, new CompanionProperty("pinned_messsages_m1_receiver_first_time_server_ts_storage", 5474, false, false));
        properties.put(3140, new CompanionProperty("pinned_messages_m1_sender", 3140, false, false));
        properties.put(3813, new CompanionProperty("pinned_messages_m1_sender_debug_expiry_duration_secs", 3813, 86400, 86400));
        properties.put(4432, new CompanionProperty("pinned_messages_sender_short_expiry_durations_enabled", 4432, false, false));
        properties.put(3732, new CompanionProperty("pinned_messages_m2_pin_max", 3732, 1, 1));
        properties.put(3141, new CompanionProperty("pinned_messages_m2", 3141, false, false));
        properties.put(1021, new CompanionProperty("admin_hfm_toggle", 1021, false, false));
        properties.put(1082, new CompanionProperty("csat_message_trigger", 1082, false, true));
        properties.put(1096, new CompanionProperty("graphql_privacy_imp_m1", 1096, false, false));
        properties.put(1104, new CompanionProperty("lthash_check_hours", 1104, 0, 0));
        properties.put(1133, new CompanionProperty("interactive_message_native_flow_killswitch", 1133, false, false));
        properties.put(1185, new CompanionProperty("sender_key_expired_logging_enabled", 1185, false, false));
        properties.put(1861, new CompanionProperty("group_size_bypassing_sampling", 1861, 100000.0, 100000.0));
        properties.put(1304, new CompanionProperty("group_size_limit", 1304, 257, 257));
        properties.put(2334, new CompanionProperty("v_id_deprecation_enabled", 2334, false, true));
        properties.put(2757, new CompanionProperty("proactive_distribute_sender_keys_enabled", 2757, false, true));
        properties.put(2860, new CompanionProperty("minimum_percentage_to_proactive_distribute_sender_keys", 2860, 200, 50));
        properties.put(1538, new CompanionProperty("address_message_native_flow_killswitch", 1538, false, false));
        properties.put(1319, new CompanionProperty("commerce_sanctioned", 1319, false, false));
        properties.put(1320, new CompanionProperty("commerce_bloks_apps_mapping", 1320, """
                {"address_message":{"app_id":"com.bloks.www.whatsapp.commerce.address_message","expiration_secs":300,"version":"1.5","supported_businesses":["+918591749310","+917977079770","+***********","+918591749310","+917977079770","+919324433533","+917669800185","+919355081749","+917217010106","+912248913727","+912068135414","+918368818019","+917827971992","+917827971988","+911244632002","+919999006542","+917982465931","+911244632030","+918920528558","+911244632026","+918920530301","+***********","+***********","+6589523673","+6597685939","+6580536071","+6531631404","+6590834813","+6588867112","+***********","+***********","+***********"]},"galaxy_message":{"flow_message_version":{"1":{"min_android_app_supported_version":"2.22.21","min_ios_app_supported_version":"2.22.16"}},"app_id":"com.bloks.www.whatsapp.commerce.galaxy_message","expiration_secs":86400,"version":"1.0","flows":{"5315848498536354":{"supported_businesses":["***********","***********","***********","***********","***********"]},"384213690506206":{"supported_businesses":["***********","908502213040"]},"785254429343710":{"supported_businesses":["***********","908502213040"]},"552092896712166":{"supported_businesses":["***********","908502213040"]},"659207712435246":{"supported_businesses":["***********","908502213040"]},"1218944301990105":{"supported_businesses":["***********","908502213040"]},"842529276647219":{"supported_businesses":["908502419528","905333860133"]},"2135286959994016":{"supported_businesses":["908502419528","905333860133"]},"465280328842503":{"supported_businesses":["908502419528","905333860133"]},"554437403152809":{"supported_businesses":["908502419528","905333860133"]},"1503880053408592":{"supported_businesses":["908502419528","905333860133"]},"1177261906521760":{"supported_businesses":["908502419528","905333860133"]},"5199590820090002":{"supported_businesses":["5511989238421"]},"615215783523200":{"supported_businesses":["5511989238421"]},"1160930701174631":{"supported_businesses":["5511989238421","555191894444"]},"2934205950056123":{"supported_businesses":["5511916282555"]},"5324889264212944":{"supported_businesses":["5511916282555"]},"3301029236883120":{"supported_businesses":["555139214004","555198849745"]},"774830743793476":{"supported_businesses":["555139214004","555198849745"]},"1493489641166601":{"supported_businesses":["555139214004","555198849745"]},"1115920052387436":{"supported_businesses":["555139214004","555198849745"]},"611775360605929":{"supported_businesses":["551147664020","551121038525"]},"1283565282457467":{"supported_businesses":["551147664020","551121038525"]},"673695173931335":{"supported_businesses":["551147664020","551121038525"]},"508459817855605":{"supported_businesses":["442034673249","447418310027"]},"639247544356777":{"supported_businesses":["442034673249","447418310027","622150851766"]},"2679509568858534":{"supported_businesses":["442034673249","447418310027","622150851766"]}}}}""", """
                {"address_message":{"app_id":"com.bloks.www.whatsapp.commerce.address_message","expiration_secs":300,"version":"1.5","supported_businesses":["+918591749310","+917977079770","+***********","+918591749310","+917977079770","+919324433533","+917669800185","+919355081749","+917217010106","+912248913727","+912068135414","+918368818019","+917827971992","+917827971988","+911244632002","+919999006542","+917982465931","+911244632030","+918920528558","+911244632026","+918920530301","+***********","+***********","+6589523673","+6597685939","+6580536071","+6531631404","+6590834813","+6588867112","+***********","+***********","+***********"]},"galaxy_message":{"flow_message_version":{"1":{"min_android_app_supported_version":"2.22.21","min_ios_app_supported_version":"2.22.16"}},"app_id":"com.bloks.www.whatsapp.commerce.galaxy_message","expiration_secs":86400,"version":"1.0","flows":{"5315848498536354":{"supported_businesses":["***********","***********","***********","***********","***********"]},"384213690506206":{"supported_businesses":["***********","908502213040"]},"785254429343710":{"supported_businesses":["***********","908502213040"]},"552092896712166":{"supported_businesses":["***********","908502213040"]},"659207712435246":{"supported_businesses":["***********","908502213040"]},"1218944301990105":{"supported_businesses":["***********","908502213040"]},"842529276647219":{"supported_businesses":["908502419528","905333860133"]},"2135286959994016":{"supported_businesses":["908502419528","905333860133"]},"465280328842503":{"supported_businesses":["908502419528","905333860133"]},"554437403152809":{"supported_businesses":["908502419528","905333860133"]},"1503880053408592":{"supported_businesses":["908502419528","905333860133"]},"1177261906521760":{"supported_businesses":["908502419528","905333860133"]},"5199590820090002":{"supported_businesses":["5511989238421"]},"615215783523200":{"supported_businesses":["5511989238421"]},"1160930701174631":{"supported_businesses":["5511989238421","555191894444"]},"2934205950056123":{"supported_businesses":["5511916282555"]},"5324889264212944":{"supported_businesses":["5511916282555"]},"3301029236883120":{"supported_businesses":["555139214004","555198849745"]},"774830743793476":{"supported_businesses":["555139214004","555198849745"]},"1493489641166601":{"supported_businesses":["555139214004","555198849745"]},"1115920052387436":{"supported_businesses":["555139214004","555198849745"]},"611775360605929":{"supported_businesses":["551147664020","551121038525"]},"1283565282457467":{"supported_businesses":["551147664020","551121038525"]},"673695173931335":{"supported_businesses":["551147664020","551121038525"]},"508459817855605":{"supported_businesses":["442034673249","447418310027"]},"639247544356777":{"supported_businesses":["442034673249","447418310027","622150851766"]},"2679509568858534":{"supported_businesses":["442034673249","447418310027","622150851766"]}}}}"""));
        properties.put(1327, new CompanionProperty("graphql_privacy_imp_m2", 1327, false, false));
        properties.put(1343, new CompanionProperty("nux_sync", 1343, false, true));
        properties.put(1377, new CompanionProperty("in_app_survey_enabled", 1377, false, true));
        properties.put(1394, new CompanionProperty("poll_creation_enabled", 1394, false, false));
        properties.put(1395, new CompanionProperty("poll_receiving_enabled", 1395, false, false));
        properties.put(2737, new CompanionProperty("poll_receiving_cag_enabled", 2737, false, false));
        properties.put(1406, new CompanionProperty("poll_name_length", 1406, 255, 255));
        properties.put(1407, new CompanionProperty("poll_option_length", 1407, 100, 100));
        properties.put(1408, new CompanionProperty("poll_option_count", 1408, 12, 12));
        properties.put(1409, new CompanionProperty("poll_offline_accuracy", 1409, 30, 30));
        properties.put(1410, new CompanionProperty("poll_cleanup_days", 1410, 31, 31));
        properties.put(1541, new CompanionProperty("poll_vote_processing_enabled", 1541, false, false));
        properties.put(1948, new CompanionProperty("poll_result_details_view_enabled", 1948, true, true));
        properties.put(2194, new CompanionProperty("poll_creation_one_on_one_chats_enabled", 2194, false, false));
        properties.put(2738, new CompanionProperty("poll_creation_cag_enabled", 2738, false, false));
        properties.put(2390, new CompanionProperty("poll_a11y_enabled", 2390, false, true));
        properties.put(2728, new CompanionProperty("enable_status_reporting", 2728, false, true));
        properties.put(1415, new CompanionProperty("group_suspend_v1_enabled", 1415, false, true));
        properties.put(2057, new CompanionProperty("group_suspend_appeal_include_entity_id_enabled", 2057, false, true));
        properties.put(2290, new CompanionProperty("block_from_chat_list", 2290, false, true));
        properties.put(2818, new CompanionProperty("community_reporting_ui_upsell_exit", 2818, true, true));
        properties.put(1417, new CompanionProperty("smb_product_price_label", 1417, "control", "control"));
        properties.put(1435, new CompanionProperty("interactive_response_message_killswitch", 1435, false, false));
        properties.put(1436, new CompanionProperty("interactive_response_message_native_flow_killswitch", 1436, false, false));
        properties.put(1464, new CompanionProperty("biz_api_voip_enabled", 1464, false, false));
        properties.put(1480, new CompanionProperty("quantity_controls_enabled", 1480, false, true));
        properties.put(1514, new CompanionProperty("catalog_categories_enabled", 1514, false, true));
        properties.put(1518, new CompanionProperty("disappearing_messages_chat_picker", 1518, false, false));
        properties.put(1322, new CompanionProperty("more_reactions_option", 1322, false, false));
        properties.put(1792, new CompanionProperty("reactions_keyboard_hides_three_flags", 1792, false, false));
        properties.put(2170, new CompanionProperty("send_reaction_from_details_pane", 2170, false, false));
        properties.put(1527, new CompanionProperty("silent_group_exit", 1527, false, true));
        properties.put(1528, new CompanionProperty("silent_group_exit_past_participants", 1528, false, true));
        properties.put(1597, new CompanionProperty("silent_group_exit_dialog", 1597, false, true));
        properties.put(1598, new CompanionProperty("silent_group_exit_sync", 1598, false, true));
        properties.put(1613, new CompanionProperty("silent_group_exit_db", 1613, false, true));
        properties.put(1600, new CompanionProperty("order_details_quick_pay", 1600, """
                {"allowed_product_type":"none"}""", """
                {"allowed_product_type":"none"}"""));
        properties.put(1599, new CompanionProperty("incentive_program_logging_enabled", 1599, false, true));
        properties.put(1612, new CompanionProperty("md_syncd_24_hour_time_format_sync_enabled", 1612, false, false));
        properties.put(2734, new CompanionProperty("md_link_device_with_phone_number_enabled", 2734, false, false));
        properties.put(3693, new CompanionProperty("md_link_device_with_phone_number_force_enabled", 3693, false, false));
        properties.put(1660, new CompanionProperty("send_cart_cta_long_button_enabled", 1660, true, true));
        properties.put(2153, new CompanionProperty("send_cart_cta_long_button_alternative_text_type", 2153, 0, 0));
        properties.put(1678, new CompanionProperty("product_search_m1_enabled", 1678, false, true));
        properties.put(1688, new CompanionProperty("smb_catalog_collections_reordering_enabled", 1688, true, true));
        properties.put(1794, new CompanionProperty("smb_catalog_collection_items_reordering_enabled", 1794, true, true));
        properties.put(1707, new CompanionProperty("is_message_secret_enabled", 1707, false, true));
        properties.put(1749, new CompanionProperty("documents_with_captions_receive", 1749, false, true));
        properties.put(1750, new CompanionProperty("documents_with_captions_send", 1750, false, true));
        properties.put(1763, new CompanionProperty("external_payments_supported_business", 1763, "+917000770007", "+918369150604,+917000770007"));
        properties.put(1766, new CompanionProperty("active_cart_discovery_enabled", 1766, false, true));
        properties.put(1767, new CompanionProperty("order_details_payment_options", 1767, """
                {"payment_options":[{"type":"JioPay","url_regex_list":["^https://www.jio.com/.*$","^https://t.jio/.*$","^http://tiny.jio.com/.*$"],"title":{"name":"jiopay_title","default_text":"Pay on Jio.com"},"subtitle":{"name":"jiopay_subtitle","default_text":"Go to Jio.com website"},"button":{"name":"jiopay_button","default_text":"Proceed to Jio.com"}}]}""", """
                {"payment_options":[{"type":"JioPay","url_regex_list":["^https://www.jio.com/.*$","^https://t.jio/.*$","^http://tiny.jio.com/.*$"],"title":{"name":"jiopay_title","default_text":"Pay on Jio.com"},"subtitle":{"name":"jiopay_subtitle","default_text":"Go to Jio.com website"},"button":{"name":"jiopay_button","default_text":"Proceed to Jio.com"}}]}"""));
        properties.put(3014, new CompanionProperty("order_details_payment_protection_link", 3014, "https://faq.whatsapp.com/725152392426717", "https://faq.whatsapp.com/725152392426717"));
        properties.put(1829, new CompanionProperty("recent_sticker_rollout_phase", 1829, 0, 0));
        properties.put(1844, new CompanionProperty("enable_client_chat_psa", 1844, false, true));
        properties.put(3182, new CompanionProperty("enable_chat_psa_auto_play_videos", 3182, false, true));
        properties.put(4033, new CompanionProperty("enable_chat_psa_forwards", 4033, false, true));
        properties.put(4659, new CompanionProperty("enable_clear_formatted_preview", 4659, false, false));
        properties.put(1846, new CompanionProperty("direct_connection_business_numbers", 1846, "***********,918591749310,917977079770", "***********,918591749310,917977079770"));
        properties.put(1853, new CompanionProperty("forward_media_with_captions", 1853, false, false));
        properties.put(3177, new CompanionProperty("append_message_when_forwarding_media", 3177, false, false));
        properties.put(3875, new CompanionProperty("append_message_when_forwarding_media_without_caption", 3875, false, false));
        properties.put(4036, new CompanionProperty("view_all_replies", 4036, false, false));
        properties.put(1867, new CompanionProperty("share_phone_number_on_cart_send_to_direct_connection_biz_enabled", 1867, true, true));
        properties.put(1875, new CompanionProperty("voice_status_receipt_enabled", 1875, true, true));
        properties.put(1921, new CompanionProperty("admin_include_message_secret_in_cag", 1921, true, true));
        properties.put(1993, new CompanionProperty("md_syncd_primary_version_sync_enabled", 1993, false, false));
        properties.put(2003, new CompanionProperty("product_catalog_qpl_logging_enabled", 2003, false, true));
        properties.put(2007, new CompanionProperty("syncd_do_not_fatal_on_snapshot_mac_mismatch_in_patches", 2007, false, false));
        properties.put(2014, new CompanionProperty("graphql_locale_remapping", 2014, "{}", "{}"));
        properties.put(2024, new CompanionProperty("product_catalog_qpl_direct_connection_status_logging_enabled", 2024, false, true));
        properties.put(2155, new CompanionProperty("favorite_sticker_rmr_sync_enabled", 2155, false, false));
        properties.put(2156, new CompanionProperty("web_link_preview_sync_enabled", 2156, false, true));
        properties.put(2189, new CompanionProperty("message_edit_receive", 2189, false, true));
        properties.put(2190, new CompanionProperty("message_edit_send", 2190, false, true));
        properties.put(3686, new CompanionProperty("caption_edit_receive", 3686, false, false));
        properties.put(3687, new CompanionProperty("caption_edit_send", 3687, false, false));
        properties.put(2983, new CompanionProperty("message_edit_window_duration_seconds", 2983, 1200, 1200));
        properties.put(3272, new CompanionProperty("message_edit_client_entry_point_limit_seconds", 3272, 900, 900));
        properties.put(4325, new CompanionProperty("message_edit_bubble_animation", 4325, false, false));
        properties.put(2193, new CompanionProperty("prekey_fetch_iq_for_missing_devices_enabled", 2193, false, false));
        properties.put(2306, new CompanionProperty("extensions_message_support_version", 2306, """
                {"1":{"min_android_app_supported_version":"2.22.21"},"2":{"min_android_app_supported_version":"2.22.23.11","min_ios_app_supported_version":"2.23.18.15"},"3":{"min_android_app_supported_version":"2.23.17.10","min_ios_app_supported_version":"2.23.18.15"}}""", """
                {"1":{"min_android_app_supported_version":"2.22.21"},"2":{"min_android_app_supported_version":"2.22.23","min_ios_app_supported_version":"2.23.18.15"},"3":{"min_android_app_supported_version":"2.23.17","min_ios_app_supported_version":"2.23.18.15"}}"""));
        properties.put(2374, new CompanionProperty("block_from_notification", 2374, false, true));
        properties.put(2378, new CompanionProperty("four_reactions_in_bubble_enabled", 2378, false, true));
        properties.put(2522, new CompanionProperty("block_entry_point_logging_enabled", 2522, false, true));
        properties.put(2573, new CompanionProperty("non_message_data_request_logging_enabled", 2573, false, true));
        properties.put(2661, new CompanionProperty("polls_fast_follow_enabled", 2661, true, true));
        properties.put(2720, new CompanionProperty("poll_chatlist_preview_enabled", 2720, false, true));
        properties.put(2662, new CompanionProperty("polls_search_support_enabled", 2662, false, true));
        properties.put(2914, new CompanionProperty("attachment_tray_logging_enabled", 2914, false, true));
        properties.put(2663, new CompanionProperty("polls_reply_support_enabled", 2663, false, true));
        properties.put(3050, new CompanionProperty("polls_single_option_control_enabled", 3050, false, true));
        properties.put(3433, new CompanionProperty("polls_single_option_sender_control_enabled", 3433, false, true));
        properties.put(3434, new CompanionProperty("polls_single_option_reciever_control_enabled", 3434, true, true));
        properties.put(3437, new CompanionProperty("polls_single_option_receiver_control_enabled", 3437, true, true));
        properties.put(3158, new CompanionProperty("polls_notification_enabled", 3158, false, false));
        properties.put(2890, new CompanionProperty("ptt_transcription_enabled", 2890, false, true));
        properties.put(3223, new CompanionProperty("attach_menu_redesign_enabled", 3223, false, false));
        properties.put(3858, new CompanionProperty("ts_navigation_community_enabled", 3858, false, false));
        properties.put(3859, new CompanionProperty("ts_bit_array_enabled", 3859, false, false));
        properties.put(4928, new CompanionProperty("ts_external_enabled", 4928, false, false));
        properties.put(4929, new CompanionProperty("ts_surface_killswitch", 4929, 0, 0));
        properties.put(3860, new CompanionProperty("ts_session_duration_ms", 3860, 600000.0, 600000.0));
        properties.put(2776, new CompanionProperty("fullscreen_animation_for_keyword", 2776, false, false));
        properties.put(2777, new CompanionProperty("syncd_additional_mutations_count", 2777, 1, 1));
        properties.put(2811, new CompanionProperty("mpm_nfm_enabled", 2811, true, true));
        properties.put(2813, new CompanionProperty("interactive_template_enabled", 2813, true, true));
        properties.put(2871, new CompanionProperty("inapp_banner_client_enabled", 2871, false, true));
        properties.put(3712, new CompanionProperty("quick_promotion_banner_client_enabled", 3712, false, false));
        properties.put(2885, new CompanionProperty("extensions_template_killswitch", 2885, false, false));
        properties.put(2891, new CompanionProperty("biz_extensions_metadata_cache_ttl_minutes", 2891, 1440, 1440));
        properties.put(2892, new CompanionProperty("biz_extensions_metadata_ban_ttl_minutes", 2892, 525600, 525600));
        properties.put(2895, new CompanionProperty("utm_tracking_enabled", 2895, false, false));
        properties.put(2896, new CompanionProperty("utm_tracking_expiration_hours", 2896, 24, 24));
        properties.put(2909, new CompanionProperty("mpm_nfm_forwarding_enabled", 2909, false, false));
        properties.put(2990, new CompanionProperty("url_hsm_redesign_enabled", 2990, true, true));
        properties.put(2994, new CompanionProperty("button_url_hsm_redesign_enabled", 2994, true, true));
        properties.put(2945, new CompanionProperty("is_internal_tester", 2945, false, true));
        properties.put(3032, new CompanionProperty("report_string_comprehension", 3032, false, true));
        properties.put(3128, new CompanionProperty("alt_device_linking_enabled", 3128, false, false));
        properties.put(3155, new CompanionProperty("mute_dialog_description", 3155, false, true));
        properties.put(3156, new CompanionProperty("mute_always_show_notification_action", 3156, false, true));
        properties.put(3192, new CompanionProperty("extensions_graphql_cta_disable", 3192, "2498088", "2498088"));
        properties.put(3198, new CompanionProperty("recent_emojis_sync", 3198, false, false));
        properties.put(3301, new CompanionProperty("syncd_report_key_stats", 3301, false, true));
        properties.put(3337, new CompanionProperty("history_sync_on_demand", 3337, false, false));
        properties.put(3642, new CompanionProperty("history_sync_on_demand_time_boundary_days", 3642, 365, 365));
        properties.put(3811, new CompanionProperty("history_sync_on_demand_message_count", 3811, 50, 50));
        properties.put(3882, new CompanionProperty("history_sync_on_demand_timeout_ms", 3882, 10000.0, 10000.0));
        properties.put(4135, new CompanionProperty("history_sync_on_demand_with_android_beta", 4135, false, false));
        properties.put(3354, new CompanionProperty("ptv_sending_enabled", 3354, false, true));
        properties.put(3355, new CompanionProperty("ptv_receiving_enabled", 3355, false, true));
        properties.put(3356, new CompanionProperty("ptv_max_duration_seconds", 3356, 60, 60));
        properties.put(3482, new CompanionProperty("ptv_autoplay_enabled", 3482, true, true));
        properties.put(3483, new CompanionProperty("ptv_autoplay_loop_limit", 3483, 3, 3));
        properties.put(4548, new CompanionProperty("ptv_nux_enabled", 4548, false, true));
        properties.put(4549, new CompanionProperty("ptv_button_persistence_enabled", 4549, false, false));
        properties.put(5292, new CompanionProperty("ptv_button_tooltip_animation_enabled", 5292, false, true));
        properties.put(5317, new CompanionProperty("ptv_button_animation_enabled", 5317, false, true));
        properties.put(5384, new CompanionProperty("ptt_button_toggle_cooldown", 5384, 0, 0));
        properties.put(5386, new CompanionProperty("ptv_button_reset_minimize_threshold", 5386, -1, -1));
        properties.put(5412, new CompanionProperty("ptv_recording_countdown_interval", 5412, 500, 500));
        properties.put(5418, new CompanionProperty("ptv_setting", 5418, false, false));
        properties.put(5419, new CompanionProperty("ptv_setting_sends_threshold", 5419, -1, -1));
        properties.put(5483, new CompanionProperty("ptv_setting_duration_threshold_seconds", 5483, 604800, 604800));
        properties.put(5507, new CompanionProperty("ptv_button_redesign_version", 5507, 0, 0));
        properties.put(3444, new CompanionProperty("template_button_improvements_on", 3444, false, true));
        properties.put(3536, new CompanionProperty("qp_campaign_client_enabled", 3536, false, false));
        properties.put(4200, new CompanionProperty("qp_push_notifications_enabled", 4200, false, false));
        properties.put(3575, new CompanionProperty("animated_emojis_enabled", 3575, false, false));
        properties.put(3579, new CompanionProperty("placeholder_message_resend", 3579, false, false));
        properties.put(3630, new CompanionProperty("is_coupon_button_enabled", 3630, false, true));
        properties.put(3631, new CompanionProperty("coupon_copy_button_url", 3631, "https://www.whatsapp.com/coupon?code=", "https://www.whatsapp.com/coupon?code="));
        properties.put(4693, new CompanionProperty("is_lto_offer_enabled", 4693, false, true));
        properties.put(5073, new CompanionProperty("lto_offer_media_aspect_ratio", 5073, 0.8, 0.8));
        properties.put(3639, new CompanionProperty("placeholder_message_resend_maximum_days_limit", 3639, 14, 14));
        properties.put(3644, new CompanionProperty("placeholder_chat_open_group_fetch", 3644, false, false));
        properties.put(3749, new CompanionProperty("placeholder_chat_open_group_fetch_size_limit", 3749, 33, 65));
        properties.put(3665, new CompanionProperty("high_quality_link_preview_enabled", 3665, false, true));
        properties.put(3690, new CompanionProperty("orders_expansion_receiver_countries_allowed", 3690, "", ""));
        properties.put(3750, new CompanionProperty("retry_receipt_error_code_enabled", 3750, false, true));
        properties.put(3771, new CompanionProperty("orders_expansion_paying_enabled", 3771, false, false));
        properties.put(4089, new CompanionProperty("cag_message_edit_receive", 4089, false, false));
        properties.put(4090, new CompanionProperty("cag_message_edit_send", 4090, false, false));
        properties.put(4091, new CompanionProperty("broadcast_message_edit_receive", 4091, false, false));
        properties.put(4092, new CompanionProperty("broadcast_message_edit_send", 4092, false, false));
        properties.put(4093, new CompanionProperty("expanded_text_formatting_enabled", 4093, false, false));
        properties.put(4150, new CompanionProperty("support_ticket_data_collection_improvements", 4150, false, false));
        properties.put(4205, new CompanionProperty("link_preview_shimmer_enabled", 4205, false, false));
        properties.put(4233, new CompanionProperty("member_name_tag_enabled", 4233, false, true));
        properties.put(4242, new CompanionProperty("support_ticket_stop_uploading_device_logs", 4242, false, false));
        properties.put(4345, new CompanionProperty("community_subgroup_join_from_system_message_enabled", 4345, false, true));
        properties.put(4668, new CompanionProperty("carousel_message_client_enabled", 4668, false, true));
        properties.put(5542, new CompanionProperty("enable_carousel_message_client_logging", 5542, false, true));
        properties.put(4697, new CompanionProperty("internal_bug_reporting_v1_enabled", 4697, false, false));
        properties.put(4849, new CompanionProperty("wae_metadata_integrity_timeout_minutes", 4849, 5, 5));
        properties.put(4893, new CompanionProperty("row_buyer_order_revamp_m0_enabled", 4893, false, true));
        properties.put(5518, new CompanionProperty("row_buyer_order_revamp_m0_nux_banner_enabled", 5518, false, true));
        properties.put(4905, new CompanionProperty("post_status_in_companion", 4905, false, false));
        properties.put(4921, new CompanionProperty("evolve_about_m1_enabled", 4921, false, false));
        properties.put(4979, new CompanionProperty("community_members_bottomsheet_enabled", 4979, false, false));
        properties.put(5000, new CompanionProperty("community_members_bottomsheet_post_creation_enabled", 5000.0, false, false));
        properties.put(5114, new CompanionProperty("buyer_initiated_order_request_variant_enabled", 5114, false, false));
        properties.put(5124, new CompanionProperty("im_nfm_dynamic_message_killswitch", 5124, false, false));
        properties.put(5171, new CompanionProperty("inbox_filters_enabled", 5171, false, false));
        properties.put(5172, new CompanionProperty("inbox_filters_favorites_enabled", 5172, false, false));
        properties.put(5173, new CompanionProperty("inbox_filters_custom_filters_enabled", 5173, false, false));
        properties.put(5190, new CompanionProperty("seller_orders_management_revamp", 5190, false, false));
        properties.put(5247, new CompanionProperty("extensions_central_config_killswitch", 5247, false, false));
        properties.put(5333, new CompanionProperty("extensions_geoblocking_enabled", 5333, false, true));
        properties.put(5553, new CompanionProperty("support_ticket_device_log_retention_period_days", 5553, 3, 3));
        properties.put(5587, new CompanionProperty("bot_3p_enabled", 5587, false, false));
        properties.put(5610, new CompanionProperty("companion_biz_label_sync_enabled", 5610, false, false));
        properties.put(5626, new CompanionProperty("saga_enabled", 5626, false, false));
        properties.put(5694, new CompanionProperty("companion_biz_quick_reply_sync_enabled", 5694, false, false));
        properties.put(2575, new CompanionProperty("project_pdf_enabled", 2575, false, true));
        properties.put(3479, new CompanionProperty("pdf_auto_start_interval_seconds", 3479, 86400, 30));
        properties.put(4100, new CompanionProperty("pdf_external_deeplink_enabled", 4100, false, true));
        properties.put(4679, new CompanionProperty("pdf_client_driven_rollout_enabled", 4679, false, true));
        properties.put(4680, new CompanionProperty("pdf_max_download_jitter_time_seconds", 4680, 180, 180));
        properties.put(4779, new CompanionProperty("pdf_md_support_enabled", 4779, false, true));
        properties.put(618, new CompanionProperty("client_group_participants_limit", 618, 257, 257));
        properties.put(812, new CompanionProperty("payment_stickers_render_enabled", 812, false, false));
        properties.put(4257, new CompanionProperty("cart_order_creation_shortcut_enabled", 4257, false, false));
        properties.put(3744, new CompanionProperty("payments_merchant_global_orders_value_props_banner_enabled", 3744, false, true));
        properties.put(5255, new CompanionProperty("payments_merchant_global_orders_value_props_banner_logging_enabled", 5255, true, true));
        properties.put(4144, new CompanionProperty("payments_br_installment_buyer_learn_more_link", 4144, "https://faq.whatsapp.com/1134168457974360", "https://faq.whatsapp.com/1134168457974360"));
        properties.put(4145, new CompanionProperty("ipayments_br_installment_seller_learn_more_link", 4145, "https://faq.whatsapp.com/253337763937767", "https://faq.whatsapp.com/253337763937767"));
        properties.put(4254, new CompanionProperty("payments_br_installment_seller_learn_more_link", 4254, "https://faq.whatsapp.com/253337763937767", "https://faq.whatsapp.com/253337763937767"));
        properties.put(5574, new CompanionProperty("seller_order_payment_request_enabled", 5574, false, false));
        properties.put(5575, new CompanionProperty("buyer_order_payment_request_enabled", 5575, false, false));
        properties.put(3051, new CompanionProperty("payments_link_to_lite_consumer_enabled", 3051, false, true));
        properties.put(4248, new CompanionProperty("payments_br_content_optimization_variant", 4248, 0, 0));
        properties.put(4781, new CompanionProperty("payments_br_pix_phase_1_seller_enabled", 4781, false, false));
        properties.put(4976, new CompanionProperty("payments_br_info_architecture_orders_hub_enabled", 4976, false, false));
        properties.put(5414, new CompanionProperty("payments_info_architecture_orders_hub_enabled", 5414, false, false));
        properties.put(808, new CompanionProperty("privacy_allow_contacts_except", 808, false, false));
        properties.put(1063, new CompanionProperty("primary_feature_sync", 1063, false, true));
        properties.put(1071, new CompanionProperty("privacy_narrative_v1", 1071, false, false));
        properties.put(1309, new CompanionProperty("add_dm_to_chat_overflow_menu", 1309, false, false));
        properties.put(1352, new CompanionProperty("keep_in_chat_receiver", 1352, false, false));
        properties.put(1353, new CompanionProperty("keep_in_chat_sender", 1353, false, false));
        properties.put(2005, new CompanionProperty("keep_in_chat_ui_content", 2005, false, false));
        properties.put(1673, new CompanionProperty("kic_orphan_cleanup_days", 1673, 31, 31));
        properties.put(2844, new CompanionProperty("supports_keep_in_chat_in_cag", 2844, true, true));
        properties.put(4042, new CompanionProperty("kic_msg_send_expiry_sec", 4042, 86400, 86400));
        properties.put(1397, new CompanionProperty("ddm_reversed_options", 1397, false, false));
        properties.put(1645, new CompanionProperty("qm_lean_msg", 1645, false, false));
        properties.put(1429, new CompanionProperty("pnh_historical_mapping_retention_seconds", 1429, 7776000.0, 7776000.0));
        properties.put(1437, new CompanionProperty("trusted_contacts_reciprocity", 1437, false, false));
        properties.put(1566, new CompanionProperty("trusted_contacts_chat_state_optimization", 1566, "old", "old"));
        properties.put(1687, new CompanionProperty("trusted_contacts_op", 1687, false, true));
        properties.put(1670, new CompanionProperty("dm_updated_system_message", 1670, false, true));
        properties.put(1698, new CompanionProperty("keep_in_chat_undo_duration_limit", 1698, 2592000.0, 2592000.0));
        properties.put(1710, new CompanionProperty("view_once_sp_receiver", 1710, false, false));
        properties.put(1711, new CompanionProperty("view_once_sp_sender", 1711, false, false));
        properties.put(1823, new CompanionProperty("pnh_ctwa", 1823, false, true));
        properties.put(2245, new CompanionProperty("pnh_indicator", 2245, false, true));
        properties.put(1892, new CompanionProperty("usync_lid", 1892, false, false));
        properties.put(3062, new CompanionProperty("pnh_pn_for_lid_chat_sync", 3062, false, true));
        properties.put(2751, new CompanionProperty("pnh_identity_verification_v3", 2751, false, false));
        properties.put(3070, new CompanionProperty("share_own_pn_sync", 3070, false, true));
        properties.put(3481, new CompanionProperty("pnh_companion_history_sync_lid_chat", 3481, false, true));
        properties.put(2304, new CompanionProperty("pnh_cag_upgrade", 2304, 0, 0));
        properties.put(2035, new CompanionProperty("cag_reactions_receive", 2035, false, false));
        properties.put(2036, new CompanionProperty("cag_reactions_send", 2036, false, false));
        properties.put(2346, new CompanionProperty("pnh_cag_show_masked_members", 2346, false, false));
        properties.put(1970, new CompanionProperty("calling_privacy_caller_offer", 1970, true, true));
        properties.put(1971, new CompanionProperty("calling_privacy_caller_send_token", 1971, true, true));
        properties.put(1972, new CompanionProperty("calling_privacy_callee", 1972, true, true));
        properties.put(3624, new CompanionProperty("group_add_ack_server", 3624, true, true));
        properties.put(2433, new CompanionProperty("pnh_cag_future_proof_banner", 2433, false, false));
        properties.put(2479, new CompanionProperty("pnh_split_threads_detection", 2479, false, false));
        properties.put(3691, new CompanionProperty("pnh_ctwa_mat_crashlog", 3691, false, false));
        properties.put(2507, new CompanionProperty("pnh_group_lid", 2507, 0, 0));
        properties.put(2561, new CompanionProperty("out_of_sync_disappearing_messages_logging", 2561, false, true));
        properties.put(2597, new CompanionProperty("dm_chat_picker_v2", 2597, false, true));
        properties.put(3305, new CompanionProperty("dm_additional_durations", 3305, false, false));
        properties.put(5309, new CompanionProperty("dm_initiator_trigger", 5309, false, true));
        properties.put(2714, new CompanionProperty("ephemeral_sync_response", 2714, false, false));
        properties.put(2919, new CompanionProperty("dmcp_manage_storage_LAUNCH", 2919, false, true));
        properties.put(2800, new CompanionProperty("settings_search", 2800, false, false));
        properties.put(2802, new CompanionProperty("enable_soox_message_receiving", 2802, false, false));
        properties.put(4922, new CompanionProperty("soox_long_press_duration_ms", 4922, 500, 500));
        properties.put(2832, new CompanionProperty("enable_soox_message_sending", 2832, false, false));
        properties.put(2939, new CompanionProperty("pnh_split_thread_case1_detection", 2939, false, true));
        properties.put(2962, new CompanionProperty("pnh_cag_block_lid_in_limbo", 2962, true, true));
        properties.put(3103, new CompanionProperty("prekey_fetch_iq_pnh_lid_enabled", 3103, false, false));
        properties.put(3366, new CompanionProperty("persisted_profile_name", 3366, false, false));
        properties.put(3458, new CompanionProperty("pnh_identity_verification_v3_pn_generation", 3458, false, false));
        properties.put(3469, new CompanionProperty("pnh_1on1_lid_expected", 3469, false, true));
        properties.put(3519, new CompanionProperty("allow_lid_contacts_storage", 3519, false, false));
        properties.put(3751, new CompanionProperty("allow_lid_contacts_new_1on1_chat", 3751, false, false));
        properties.put(3752, new CompanionProperty("allow_lid_contacts_add_to_group", 3752, false, false));
        properties.put(3762, new CompanionProperty("allow_lid_contacts_calling", 3762, false, false));
        properties.put(3763, new CompanionProperty("allow_lid_contacts_privacy_settings", 3763, false, false));
        properties.put(3789, new CompanionProperty("allow_share_lid_contacts_vcard", 3789, false, false));
        properties.put(3790, new CompanionProperty("allow_parse_lid_contacts_vcard", 3790, false, false));
        properties.put(3603, new CompanionProperty("rabbit_enabled", 3603, false, false));
        properties.put(3872, new CompanionProperty("pnh_prevent_undefined_lid_chat_origin", 3872, false, false));
        properties.put(3962, new CompanionProperty("first_message_experience", 3962, false, false));
        properties.put(5263, new CompanionProperty("first_message_experience_v2", 5263, false, false));
        properties.put(4314, new CompanionProperty("privacy_tips_killswitch", 4314, false, false));
        properties.put(3995, new CompanionProperty("privacy_tips_groups_build", 3995, false, false));
        properties.put(3996, new CompanionProperty("privacy_tips_callers_build", 3996, false, false));
        properties.put(3997, new CompanionProperty("privacy_tips_status_build", 3997, false, false));
        properties.put(3998, new CompanionProperty("privacy_tips_profile_build", 3998, false, false));
        properties.put(3999, new CompanionProperty("unified_e2ee_copy_build", 3999, false, false));
        properties.put(4000, new CompanionProperty("unified_e2ee_ui_build", 4000.0, false, false));
        properties.put(4869, new CompanionProperty("unified_e2ee_copy_launch", 4869, false, false));
        properties.put(4870, new CompanionProperty("unified_e2ee_ui_launch", 4870, false, false));
        properties.put(5111, new CompanionProperty("unified_e2ee_bottomsheet", 5111, false, false));
        properties.put(5112, new CompanionProperty("unified_e2ee_security_page", 5112, false, false));
        properties.put(5113, new CompanionProperty("unified_e2ee_backup_page", 5113, false, false));
        properties.put(4131, new CompanionProperty("dm_reliability_refactor", 4131, false, false));
        properties.put(5580, new CompanionProperty("dm_reliability_logging", 5580, false, false));
        properties.put(4178, new CompanionProperty("pnh_1on1_report_lid_message_send", 4178, false, false));
        properties.put(4214, new CompanionProperty("privacy_tip_expiration_min", 4214, 10080, 10080));
        properties.put(4297, new CompanionProperty("soox_media_send_keep_old_button", 4297, false, false));
        properties.put(4495, new CompanionProperty("pnh_cag_disable_reactions_group_size", 4495, 10000.0, 10000.0));
        properties.put(5056, new CompanionProperty("pnh_cag_disable_polls_group_size", 5056, 10000.0, 10000.0));
        properties.put(4745, new CompanionProperty("username_creation", 4745, false, false));
        properties.put(4746, new CompanionProperty("username_contact_display", 4746, false, false));
        properties.put(4747, new CompanionProperty("username_change", 4747, false, false));
        properties.put(4748, new CompanionProperty("username_1on1_chat", 4748, false, false));
        properties.put(4749, new CompanionProperty("username_group_participants", 4749, false, false));
        properties.put(5290, new CompanionProperty("username_usync", 5290, false, false));
        properties.put(5189, new CompanionProperty("system_msg_update", 5189, false, false));
        properties.put(5513, new CompanionProperty("soox_vo_moving_hide_learn_more", 5513, true, false));
        properties.put(864, new CompanionProperty("sticker_md_favorite_stickers_enabled", 864, false, false));
        properties.put(1469, new CompanionProperty("smb_orange_enabled", 1469, false, false));
        properties.put(1483, new CompanionProperty("smb_melon_display_enabled", 1483, false, false));
        properties.put(1484, new CompanionProperty("smb_melon_management_enabled", 1484, false, false));
        properties.put(1525, new CompanionProperty("call_only_primary_device_limit_exceeded", 1525, false, false));
        properties.put(1591, new CompanionProperty("smb_premium_md_limit_perf_tracker_enabled", 1591, false, true));
        properties.put(1583, new CompanionProperty("smb_billing_enabled", 1583, false, false));
        properties.put(1619, new CompanionProperty("smb_billing_premium_access_config", 1619, "", ""));
        properties.put(1672, new CompanionProperty("smb_billing_logging_enabled", 1672, false, true));
        properties.put(1669, new CompanionProperty("smb_melon_logging_enabled", 1669, false, true));
        properties.put(1701, new CompanionProperty("smb_dcp_enabled", 1701, false, false));
        properties.put(1849, new CompanionProperty("smb_custom_url_display_v2_enabled", 1849, false, true));
        properties.put(1438, new CompanionProperty("smb_multi_device_agents_enabled", 1438, false, true));
        properties.put(1981, new CompanionProperty("smb_multi_device_message_attribution_enabled", 1981, false, true));
        properties.put(1671, new CompanionProperty("smb_multi_device_agents_logging_enabled", 1671, false, true));
        properties.put(1897, new CompanionProperty("smb_multi_device_agents_logging_V2_enabled", 1897, false, true));
        properties.put(1798, new CompanionProperty("smb_md_agent_chat_assignment_enabled", 1798, false, true));
        properties.put(2157, new CompanionProperty("smb_md_agent_chat_assignment_system_messages_enabled", 2157, false, true));
        properties.put(2709, new CompanionProperty("smb_md_agent_chat_assignment_system_messages_logging_v2_enabled", 2709, false, true));
        properties.put(2778, new CompanionProperty("smb_md_agent_chat_assignment_system_messages_chats_reorder_enabled", 2778, false, true));
        properties.put(2787, new CompanionProperty("smb_md_agent_chat_assignment_chats_reorder_on_chat_assignment_enabled", 2787, false, true));
        properties.put(2788, new CompanionProperty("smb_md_agent_chat_assignment_chats_reorder_on_chat_unassignment_enabled", 2788, false, true));
        properties.put(2207, new CompanionProperty("smb_md_agent_chat_assignment_nux_impressions", 2207, 0, 3));
        properties.put(2976, new CompanionProperty("smb_md_agent_chat_assignment_chat_list_new_label_enabled", 2976, false, true));
        properties.put(2320, new CompanionProperty("coex_biz_states_sys_msg_enabled", 2320, false, true));
        properties.put(2582, new CompanionProperty("smb_biz_profile_custom_url", 2582, false, false));
        properties.put(2583, new CompanionProperty("smb_biz_profile_custom_url_notifications", 2583, false, false));
        properties.put(2908, new CompanionProperty("smb_md_agent_chat_assignment_notifications_enabled", 2908, false, true));
        properties.put(3046, new CompanionProperty("smb_marketing_messages_enabled", 3046, false, true));
        properties.put(3113, new CompanionProperty("smb_marketing_messages_product_ids", 3113, "", ""));
        properties.put(3124, new CompanionProperty("smb_rambutan_enabled", 3124, false, true));
        properties.put(3125, new CompanionProperty("smb_rambutan_product_ids", 3125, "", ""));
        properties.put(4005, new CompanionProperty("smb_premium_messages_spam_report_enabled", 4005, false, true));
        properties.put(4596, new CompanionProperty("web_premium_messages_interactivity_rendering_enabled", 4596, false, true));
        properties.put(4657, new CompanionProperty("smb_premium_messages_click_logging_enabled", 4657, false, true));
        properties.put(4942, new CompanionProperty("smba_premium_messages_interactivity_catalog_cta_consumer_enabled", 4942, false, true));
        properties.put(4957, new CompanionProperty("smb_premium_messages_interactivity_catalog_cta_consumer_enabled", 4957, false, true));
        properties.put(5044, new CompanionProperty("smb_premium_messages_url_cta_alert_dialog_enabled", 5044, true, true));
        properties.put(5318, new CompanionProperty("premium_blue_enabled", 5318, false, false));
        properties.put(5276, new CompanionProperty("blue_enabled", 5276, false, false));
        properties.put(5295, new CompanionProperty("blue_education_enabled", 5295, false, false));
        properties.put(5277, new CompanionProperty("newsletter_blue_enabled", 5277, false, false));
        properties.put(5296, new CompanionProperty("newsletter_blue_education_enabled", 5296, false, false));
        properties.put(2249, new CompanionProperty("mex_phase3_enabled", 2249, false, false));
        properties.put(2250, new CompanionProperty("mex_phase3_status_flags", 2250, 0, 0));
        properties.put(3604, new CompanionProperty("mex_newsletter_killswitch", 3604, false, false));
        properties.put(5437, new CompanionProperty("mex_native_client_enabled", 5437, false, false));
        properties.put(3605, new CompanionProperty("mex_newsletter_flags", 3605, 0, 0));
        properties.put(2980, new CompanionProperty("groove_enabled_web", 2980, false, false));
        properties.put(3385, new CompanionProperty("newsletter_enabled", 3385, false, false));
        properties.put(3020, new CompanionProperty("newsletter_enabled_web", 3020, false, false));
        properties.put(3148, new CompanionProperty("newsletter_reporting_enabled", 3148, false, true));
        properties.put(3149, new CompanionProperty("newsletter_suspend_enabled", 3149, false, true));
        properties.put(4219, new CompanionProperty("channels_restricted_updates_enabled", 4219, false, true));
        properties.put(5161, new CompanionProperty("channels_geosuspend_enabled", 5161, false, true));
        properties.put(5216, new CompanionProperty("channels_geosuspend_admin_alerts_enabled", 5216, false, true));
        properties.put(5621, new CompanionProperty("channel_info_admin_metadata_fetching_enabled", 5621, false, true));
        properties.put(3209, new CompanionProperty("allow_nl_linkpreview", 3209, true, true));
        properties.put(3607, new CompanionProperty("newsletter_creation_enabled", 3607, false, false));
        properties.put(3778, new CompanionProperty("newsletter_media_autodownload_mode", 3778, 3, 3));
        properties.put(4369, new CompanionProperty("newsletter_media_autodownload_jitter_multiplier", 4369, 5000.0, 1000.0));
        properties.put(4370, new CompanionProperty("newsletter_media_autodownload_queue_max_concurrency", 4370, 5, 5));
        properties.put(4479, new CompanionProperty("newsletter_media_priority_queue_incoming_max_size", 4479, 32, 32));
        properties.put(3617, new CompanionProperty("nl_df_gid", 3617, "", ""));
        properties.put(3618, new CompanionProperty("nl_crt_df_gid", 3618, "", "120363080354356818"));
        properties.put(3810, new CompanionProperty("newsletter_tos_notice_id", 3810, "20601216", "20601216"));
        properties.put(5448, new CompanionProperty("newsletter_tos_notice_version", 5448, 1, 1));
        properties.put(3834, new CompanionProperty("newsletter_creation_tos_id", 3834, "20601217", "20601217"));
        properties.put(5449, new CompanionProperty("newsletter_creation_tos_version", 5449, "20601217", "20601217"));
        properties.put(5456, new CompanionProperty("newsletter_creation_tos_version_v2", 5456, 1, 1));
        properties.put(3835, new CompanionProperty("newsletter_creation_nux_id", 3835, "20601218", "20601218"));
        properties.put(5450, new CompanionProperty("newsletter_creation_nux_version", 5450, "20601218", "20601218"));
        properties.put(5457, new CompanionProperty("newsletter_creation_nux_version_v2", 5457, 1, 1));
        properties.put(5597, new CompanionProperty("newsletter_tos_notice_id_smb_web", 5597, "20601216", "20601216"));
        properties.put(5598, new CompanionProperty("newsletter_creation_tos_id_smb_web", 5598, "20601217", "20601217"));
        properties.put(3877, new CompanionProperty("channels_enabled", 3877, 0, 0));
        properties.put(3878, new CompanionProperty("channels_creation_enabled", 3878, 0, 2));
        properties.put(3879, new CompanionProperty("channels_directory_enabled", 3879, 0, 2));
        properties.put(4356, new CompanionProperty("channels_recommended_enabled", 4356, 0, 2));
        properties.put(4590, new CompanionProperty("channels_view_counts_enabled", 4590, false, true));
        properties.put(4721, new CompanionProperty("channel_view_counts_enabled", 4721, 0, 3));
        properties.put(4648, new CompanionProperty("channel_views_duration_milliseconds", 4648, 1000.0, 1000.0));
        properties.put(4722, new CompanionProperty("channel_playable_message_views_duration_milliseconds", 4722, 3000.0, 3000.0));
        properties.put(4271, new CompanionProperty("channels_recommended_cache_ttl_ms", 4271, 6.048E8, 8.64E7));
        properties.put(3880, new CompanionProperty("show_channels_not_available_dialog", 3880, false, true));
        properties.put(3900, new CompanionProperty("newsletter_supported_message_types", 3900, """
                {"supported": [1, 2, 3, 9]}""", """
                {"supported": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}"""));
        properties.put(3919, new CompanionProperty("channel_supported_message_types", 3919, "1, 2, 3, 9, 10", "1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15"));
        properties.put(4282, new CompanionProperty("directory_sort_kill_switch", 4282, true, true));
        properties.put(4283, new CompanionProperty("directory_search_kill_switch", 4283, true, true));
        properties.put(5303, new CompanionProperty("channels_directory_v2_cache_ttl_ms", 5303, 7200000.0, 3600000.0));
        properties.put(5304, new CompanionProperty("channels_directory_v2_cache_refresh_interval_ms", 5304, 1800000.0, 600000.0));
        properties.put(4306, new CompanionProperty("channel_reactions_enabled", 4306, false, true));
        properties.put(4633, new CompanionProperty("channel_reactions_sending_enabled", 4633, false, true));
        properties.put(4887, new CompanionProperty("channel_reactions_settings_enabled", 4887, false, true));
        properties.put(5274, new CompanionProperty("channel_reactions_settings_none_option_enabled", 5274, false, false));
        properties.put(5185, new CompanionProperty("channel_reactions_sender_list_enabled", 5185, false, true));
        properties.put(4699, new CompanionProperty("reactions_allowlisted_channels", 4699, "", "120363144141162927,120363160538286018"));
        properties.put(4307, new CompanionProperty("channel_follower_list_enabled", 4307, false, true));
        properties.put(5182, new CompanionProperty("channels_followers_list_cache_refresh_seconds", 5182, 60, 60));
        properties.put(5217, new CompanionProperty("channels_followers_list_cache_refresh_milliseconds", 5217, 60000.0, 60000.0));
        properties.put(4308, new CompanionProperty("recommended_channels_cache_max_ttl", 4308, 0, 0));
        properties.put(4309, new CompanionProperty("recommended_channels_background_refresh", 4309, 1.44E7, 1800000.0));
        properties.put(4326, new CompanionProperty("channel_pull_message_updates_threshold_seconds", 4326, 120, 120));
        properties.put(4338, new CompanionProperty("channel_forward_to_chat_enabled", 4338, false, true));
        properties.put(4860, new CompanionProperty("channel_forward_to_chat_link_enabled", 4860, false, true));
        properties.put(4357, new CompanionProperty("channels_waitlist_enabled", 4357, false, true));
        properties.put(4506, new CompanionProperty("channels_updates_tab_logging_enabled", 4506, false, true));
        properties.put(4632, new CompanionProperty("channels_waitlist_logging_enabled", 4632, false, true));
        properties.put(4635, new CompanionProperty("channels_dyi_enabled", 4635, false, true));
        properties.put(4866, new CompanionProperty("channels_dyi_max_file_size_in_bytes_warning_threshold", 4866, 1.0E9, 1.0E9));
        properties.put(5488, new CompanionProperty("channels_dyi_logging_enabled", 5488, false, true));
        properties.put(4644, new CompanionProperty("channel_forward_to_chat_v2_enabled", 4644, false, false));
        properties.put(4653, new CompanionProperty("channels_large_number_format_enabled", 4653, false, true));
        properties.put(4682, new CompanionProperty("channel_forward_to_chat_v2_message_navigation_enabled", 4682, false, true));
        properties.put(4683, new CompanionProperty("channel_forward_to_chat_v2_new_ui_enabled", 4683, false, true));
        properties.put(4684, new CompanionProperty("channels_view_counts_display_to_followers_enabled", 4684, false, true));
        properties.put(4760, new CompanionProperty("channels_send_view_receipt_enabled", 4760, false, true));
        properties.put(4782, new CompanionProperty("channels_forwarding_logging_enabled", 4782, false, true));
        properties.put(4783, new CompanionProperty("channels_directory_logging_enabled", 4783, false, true));
        properties.put(4784, new CompanionProperty("channels_creation_logging_enabled", 4784, false, true));
        properties.put(5015, new CompanionProperty("channels_filter_out_subscribed_in_directory_null_state", 5015, false, true));
        properties.put(5040, new CompanionProperty("ts_navigation_channels_enabled", 5040, false, true));
        properties.put(5262, new CompanionProperty("channel_core_event_logging_enabled", 5262, false, true));
        properties.put(5041, new CompanionProperty("channel_link_in_nav_bar_enabled", 5041, false, true));
        properties.put(5096, new CompanionProperty("channels_recommended_v2_ui_enabled", 5096, false, true));
        properties.put(5464, new CompanionProperty("channels_recommended_v2_recently_followed_channels_below_enabled", 5464, false, true));
        properties.put(5126, new CompanionProperty("channels_directory_v2_enabled", 5126, false, true));
        properties.put(5127, new CompanionProperty("channels_directory_v2_filter_types", 5127, "", "1, 2, 3, 4, 5, 6"));
        properties.put(5158, new CompanionProperty("channels_admin_context_card_enabled", 5158, false, true));
        properties.put(5174, new CompanionProperty("channels_message_edit_enabled", 5174, false, true));
        properties.put(5188, new CompanionProperty("channels_message_link_enabled", 5188, false, true));
        properties.put(5203, new CompanionProperty("channels_directory_fetch_limit", 5203, 50, 50));
        properties.put(5204, new CompanionProperty("channels_directory_search_debounce_ms", 5204, 250, 250));
        properties.put(5205, new CompanionProperty("recommended_channels_fetch_limit", 5205, 20, 20));
        properties.put(5208, new CompanionProperty("channels_edit_backwards_compatibility", 5208, false, true));
        properties.put(5287, new CompanionProperty("channels_hide_news_url_preview", 5287, false, true));
        properties.put(5402, new CompanionProperty("large_number_format_uses_generic_plural", 5402, true, true));
        properties.put(5471, new CompanionProperty("channels_directory_v2_logging_enabled", 5471, false, true));
        properties.put(5491, new CompanionProperty("channels_share_link_logging_enabled", 5491, false, true));
        properties.put(5492, new CompanionProperty("channels_forward_logging_v2_enabled", 5492, false, true));
        properties.put(5487, new CompanionProperty("channels_directory_pagination_enabled", 5487, false, true));
        properties.put(5489, new CompanionProperty("updates_tab_open_channels_fields_logging_enabled", 5489, false, true));
        properties.put(5494, new CompanionProperty("channels_max_messages_batch_pull", 5494, 100, 100));
        properties.put(5511, new CompanionProperty("channels_hq_link_preview", 5511, false, false));
        properties.put(5533, new CompanionProperty("channels_poll_creation_enabled", 5533, false, true));
        properties.put(5534, new CompanionProperty("channels_poll_single_option_control_enable", 5534, false, true));
        properties.put(5551, new CompanionProperty("channels_web_bootstrap_timeout_enabled", 5551, false, true));
        properties.put(5625, new CompanionProperty("channels_media_cache_setting_enabled", 5625, false, false));
        properties.put(5643, new CompanionProperty("channels_send_album_enabled", 5643, false, true));
        properties.put(5646, new CompanionProperty("channels_message_loading_indicators_enabled", 5646, false, true));
        properties.put(3710, new CompanionProperty("otp_ttl_inject_receipt_enabled", 3710, false, true));
        properties.put(3827, new CompanionProperty("unified_otp_copy_code_url", 3827, "https://www.whatsapp.com/otp/copy/", "https://www.whatsapp.com/otp/copy/"));
        properties.put(3828, new CompanionProperty("unified_otp_retriever_url", 3828, "https://www.whatsapp.com/otp/code", "https://www.whatsapp.com/otp/code"));
        properties.put(4330, new CompanionProperty("web_otp_copy_code_disabled", 4330, false, false));
        properties.put(3514, new CompanionProperty("lid_groups_ougtoing_explict_address_mode", 3514, false, true));
        properties.put(3615, new CompanionProperty("lid_groups_outgoing_explict_address_mode", 3615, false, true));
        properties.put(3645, new CompanionProperty("lid_groups_new_group_creation", 3645, false, false));
        properties.put(3688, new CompanionProperty("lid_groups_handle_server_addressing_mode", 3688, false, false));
        properties.put(3876, new CompanionProperty("lid_groups_create_lid_individual_chats", 3876, false, false));
        properties.put(3803, new CompanionProperty("lid_groups_outgoing_explicit_address_mode", 3803, false, true));
        properties.put(3804, new CompanionProperty("lid_groups_aggregate_participant_change_system_message", 3804, false, false));
        properties.put(4162, new CompanionProperty("lid_groups_message_send_validation", 4162, false, true));
        properties.put(4476, new CompanionProperty("pnh_copy_identity_keys_and_devices", 4476, false, true));
        properties.put(4533, new CompanionProperty("pnh_sync_identity_keys_and_devices", 4533, false, true));
        properties.put(5555, new CompanionProperty("invalid_hosted_companion_nack_enabled", 5555, false, true));
        properties.put(5623, new CompanionProperty("lid_outgoing_msg_attach_meta_tag", 5623, false, false));
        properties.put(3180, new CompanionProperty("group_suspend_v2_enabled", 3180, false, true));
        properties.put(3988, new CompanionProperty("enable_status_report_and_block", 3988, false, true));
        properties.put(5245, new CompanionProperty("report_block_classification_logging_enabled", 5245, false, true));
        properties.put(5716, new CompanionProperty("rt_validate_message_type", 5716, false, false));
        properties.put(5717, new CompanionProperty("rt_send_reporting_tag", 5717, false, false));
        properties.put(5718, new CompanionProperty("rt_receive_reporting_tag", 5718, false, false));
        properties.put(3471, new CompanionProperty("df_config", 3471, "", ""));
        properties.put(3472, new CompanionProperty("df_enabled", 3472, false, false));
        properties.put(4873, new CompanionProperty("wabai_message_rendering_enabled", 4873, false, false));
        properties.put(5215, new CompanionProperty("wabai_message_feedback_enabled", 5215, false, false));
        properties.put(5224, new CompanionProperty("wabai_marketing_message_content_gen_enabled", 5224, false, false));
        properties.put(5330, new CompanionProperty("ctwa_content_gen_enabled", 5330, false, false));
        //noinspection Java9CollectionFactory
        PROPERTIES = Collections.unmodifiableMap(properties);
    }

    public static boolean anyMatch(String input, String regex) {
        return Pattern.compile(regex)
                .matcher(input)
                .results()
                .findAny()
                .isPresent();
    }
}
