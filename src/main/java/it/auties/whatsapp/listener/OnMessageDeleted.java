package it.auties.whatsapp.listener;

import it.auties.whatsapp.model.info.ChatMessageInfo;

public interface OnMessageDeleted extends Listener {
    /**
     * Called when a message is deleted
     *
     * @param info     the message that was deleted
     * @param everyone whether this message was deleted by you only for yourself or whether the
     *                 message was permanently removed
     */
    @Override
    void onMessageDeleted(ChatMessageInfo info, boolean everyone);
}