<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.github.auties00</groupId>
    <artifactId>cobalt</artifactId>
    <version>0.1</version>
    <name>${project.groupId}:${project.artifactId}</name>
    <description>Standalone fully-featured Whatsapp Web API for Java and Kotlin</description>
    <url>https://github.com/Auties00/Cobalt</url>
    <developers>
        <developer>
            <name><PERSON></name>
            <email><EMAIL></email>
        </developer>
    </developers>
    <licenses>
        <license>
            <name>MIT License</name>
            <url>https://www.opensource.org/licenses/mit-license.php</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <url>https://github.com/Auties00/Cobalt/tree/master</url>
        <connection>scm:git:https://github.com/Auties00/Cobalt.git</connection>
        <developerConnection>scm:ssh:https://github.com/Auties00/Cobalt.git</developerConnection>
    </scm>

    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://s01.oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <url>https://s01.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>sign</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <!-- Attach the sourcecode -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>${maven.source.plugin.version}</version>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <!-- Generate and attach javadocs from source -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>${maven.javadoc.plugin.version}</version>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <release>${java.version}</release>
                            <failOnError>true</failOnError>
                            <show>private</show>
                            <debug>true</debug>
                            <verbose>true</verbose>
                        </configuration>
                    </plugin>

                    <!-- Sign the artifacts -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>${maven.gpg.plugin.version}</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <keyname>${gpg.keyname}</keyname>
                                    <passphraseServerId>${gpg.passphrase}</passphraseServerId>
                                    <gpgArguments>
                                        <arg>--pinentry-mode</arg>
                                        <arg>loopback</arg>
                                    </gpgArguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>

                    <!-- Deploy the newsletters-->
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <version>${maven.nexus.plugin.version}</version>
                        <extensions>true</extensions>
                        <configuration>
                            <serverId>ossrh</serverId>
                            <nexusUrl>https://s01.oss.sonatype.org/</nexusUrl>
                            <autoReleaseAfterClose>true</autoReleaseAfterClose>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>21</java.version>
        <jilt.version>1.2</jilt.version>
        <maven.surefire.plugin.version>3.0.0-M9</maven.surefire.plugin.version>
        <maven.gpg.plugin.version>3.0.1</maven.gpg.plugin.version>
        <maven.compiler.plugin.version>3.11.0</maven.compiler.plugin.version>
        <maven.source.plugin.version>3.2.1</maven.source.plugin.version>
        <maven.javadoc.plugin.version>3.5.0</maven.javadoc.plugin.version>
        <maven.nexus.plugin.version>1.6.13</maven.nexus.plugin.version>
        <bouncy.castle.version>1.70</bouncy.castle.version>
        <zxing.version>3.5.1</zxing.version>
        <protoc.version>3.0.1</protoc.version>
        <checker.framework.version>3.37.0</checker.framework.version>
        <junit.version>5.10.0-M1</junit.version>
        <jna.version>5.13.0</jna.version>
        <jackson.version>2.15.2</jackson.version>
        <curve25519.version>1.1</curve25519.version>
        <classgraph.version>4.8.160</classgraph.version>
        <reflection.version>1.2</reflection.version>
        <lazysodium.version>5.1.4</lazysodium.version>
        <linkpreview.version>2.3</linkpreview.version>
        <vcard.version>0.12.1</vcard.version>
        <qr.terminal.version>2.2</qr.terminal.version>
        <libphonenumber.version>8.13.13</libphonenumber.version>
        <pdfbox.version>2.0.27</pdfbox.version>
        <poi.version>5.2.3</poi.version>
        <netty.buffer.version>4.1.93.Final</netty.buffer.version>
        <sl4j.version>2.0.7</sl4j.version>
        <log4j.version>2.20.0</log4j.version>
        <maven.versions.version>2.15.0</maven.versions.version>
        <apk.parser.version>2.6.10</apk.parser.version>
    </properties>

    <build>
        <plugins>
            <!-- Dependencies version updater -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>${maven.versions.version}</version>
            </plugin>

            <!-- Compile the project-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <release>${java.version}</release>
                    <annotationProcessorPaths>
                        <annotationProcessorPath>
                            <groupId>com.github.auties00</groupId>
                            <artifactId>protobuf-serialization-plugin</artifactId>
                            <version>${protoc.version}</version>
                        </annotationProcessorPath>
                        <annotationProcessorPath>
                            <groupId>org.checkerframework</groupId>
                            <artifactId>checker</artifactId>
                            <version>${checker.framework.version}</version>
                        </annotationProcessorPath>
                        <annotationProcessorPath>
                            <groupId>cc.jilt</groupId>
                            <artifactId>jilt</artifactId>
                            <version>${jilt.version}</version>
                        </annotationProcessorPath>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <failOnError>true</failOnError>
                </configuration>
            </plugin>

            <!-- Test the library to be sure that everything works-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven.surefire.plugin.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-engine</artifactId>
                        <version>${junit.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <!-- Generate and print QR code (Web API) -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>${zxing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.auties00</groupId>
            <artifactId>qr-terminal</artifactId>
            <version>${qr.terminal.version}</version>
        </dependency>

        <!-- Cryptography -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>${bouncy.castle.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>${bouncy.castle.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.auties00</groupId>
            <artifactId>curve25519</artifactId>
            <version>${curve25519.version}</version>
        </dependency>

        <!-- Protobuf serialization (used by Whatsapp) -->
        <dependency>
            <groupId>com.github.auties00</groupId>
            <artifactId>protobuf-base</artifactId>
            <version>${protoc.version}</version>
        </dependency>

        <!-- Smile serialization (used to persist sessions) -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-smile</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-parameter-names</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${jackson.version}</version>
        </dependency>


        <!-- Scan for listeners - @RegisterListener -->
        <dependency>
            <groupId>io.github.classgraph</groupId>
            <artifactId>classgraph</artifactId>
            <version>${classgraph.version}</version>
        </dependency>

        <!-- Parse phone numbers (Mobile API) -->
        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>libphonenumber</artifactId>
            <version>${libphonenumber.version}</version>
        </dependency>

        <!-- Parse Android APK for token generation (Mobile API) -->
        <dependency>
            <groupId>net.dongliu</groupId>
            <artifactId>apk-parser</artifactId>
            <version>${apk.parser.version}</version>
        </dependency>

        <!-- Generate message previews -->
        <dependency>
            <groupId>com.github.auties00</groupId>
            <artifactId>link-preview</artifactId>
            <version>${linkpreview.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>${pdfbox.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>${poi.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.googlecode.ez-vcard</groupId>
            <artifactId>ez-vcard</artifactId>
            <version>${vcard.version}</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.goterl</groupId>
            <artifactId>lazysodium-java</artifactId>
            <version>${lazysodium.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>${jna.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcmail-jdk15on</artifactId>
            <version>${bouncy.castle.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpg-jdk15on</artifactId>
            <version>${bouncy.castle.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>